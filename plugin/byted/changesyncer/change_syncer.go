package changesyncer

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"code.byted.org/vecode/sdk/client"
	"code.byted.org/vecode/sdk/service/vecode"
	"code.byted.org/vecode/sdk/service/vecode/types/vecode/merge_request"
	"code.byted.org/vecode/sdk/service/vecode/types/vecode/repository"
	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/multierr"

	changedal "code.byted.org/vecode/staircase/module/change/dal"
	changemodel "code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/module/sync/changesyncer"
	"code.byted.org/vecode/staircase/plugin/byted/code"
	"code.byted.org/vecode/staircase/util/git"
)

var _ changesyncer.ChangeSyncer = new(ChangeSyncer)

type ChangeSyncer struct {
	Git           git.Git
	ChangeDAO     changedal.ChangeDAO
	Authenticator code.Authenticator
}

func (s *ChangeSyncer) SyncChanges(ctx context.Context, opt *changesyncer.SyncChangesOpt) (*changesyncer.SyncChangesResult, error) {
	changes := opt.Changes
	if len(changes) == 0 {
		return new(changesyncer.SyncChangesResult), nil
	}
	// calculate global push options
	globalPushOptions := []string{"merge_request.change_mode=multi_commits"}
	if lo.Contains(opt.Options, "draft") {
		globalPushOptions = append(globalPushOptions, "merge_request.draft=true")
	}
	// list remote changes
	if opt.ProgressWriter != nil {
		_, err := fmt.Fprintf(opt.ProgressWriter, "Fetching latest changes from '%s'...\n", opt.Remote)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to write progress")
		}
	}
	remoteStatus, err := s.ListChanges(ctx, &changesyncer.ListChangesOpt{
		RepoPath: opt.RepoPath,
		Remote:   opt.Remote,
		Changes:  opt.Changes,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list remote changes")
	}
	remoteChangesByID := lo.KeyBy(remoteStatus.Changes, func(item *changemodel.Change) changemodel.ChangeID {
		return item.ID
	})
	// push
	var pushed bool
	changeListsByTargetBranch := changemodel.GroupSortedChangesByTargetBranch(changes)
	for targetBranch, changes := range changeListsByTargetBranch {
		if len(changes) == 0 {
			continue
		}

		// filter changes needed to push
		botChange := changes[0]
		remoteChange := remoteChangesByID[botChange.ID]
		if remoteChange != nil && pointer.Get(botChange.TargetBranchName) == pointer.Get(remoteChange.TargetBranchName) {
			// bot change's target branch is not changed, so we can filter changes whose title or code has changed.
			// this may not be accurate, but enough for most cases.
			changes = lo.Filter(changes, func(change *changemodel.Change, index int) bool {
				remoteChange := remoteChangesByID[change.ID]
				return !change.Equal(remoteChange)
			})
			if len(changes) == 0 {
				continue
			}
		}

		pushed = true
		var err error
		// build commit to push
		commitID := changes[0].LatestVersion().CommitID
		if len(changes) > 1 {
			commitID, err = s.Git.CommitTree(ctx, &git.CommitTreeOpt{
				RepoPath: opt.RepoPath,
				Revision: commitID, // just a placeholder
				Message:  "merge",
				ParentRevisions: lo.Map(changes, func(item *changemodel.Change, _ int) string {
					return item.LatestVersion().CommitID
				}),
			})
			if err != nil {
				return nil, errors.WithMessage(err, "failed to commit tree")
			}
		}
		// build push options
		pushOptions := globalPushOptions
		for i, change := range changes {
			pushOptions = append(pushOptions, fmt.Sprintf("merge_requests.%d.title=%s", i, change.Title))
			pushOptions = append(pushOptions, fmt.Sprintf("merge_requests.%d.change_id=%s", i, string(change.ID)))
			pushOptions = append(pushOptions, fmt.Sprintf("merge_requests.%d.commit_id=%s", i, change.LatestVersion().CommitID))
			pushOptions = append(pushOptions, fmt.Sprintf("merge_requests.%d.base_commit_id=%s", i, change.LatestVersion().BaseCommitID))
			if change.ParentID != nil {
				pushOptions = append(pushOptions, fmt.Sprintf("merge_requests.%d.parent_change_id=%s", i, string(*change.ParentID)))
			}
		}
		// push and create remote merge requests
		if opt.ProgressWriter != nil {
			_, err := fmt.Fprintf(opt.ProgressWriter, "Submitting changes based on '%s' to '%s'...\n", targetBranch, opt.Remote)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to write progress")
			}
		}
		err = s.Git.Push(ctx, &git.PushOpt{
			RepoPath:       opt.RepoPath,
			Remote:         opt.Remote,
			RefSpecs:       []string{fmt.Sprintf("%s:refs/for/%s", commitID, targetBranch)},
			PushOptions:    pushOptions,
			ProgressWriter: opt.ProgressWriter,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to push")
		}
	}

	result := new(changesyncer.SyncChangesResult)
	if pushed {
		// list remote changes
		if opt.ProgressWriter != nil {
			_, err := fmt.Fprintf(opt.ProgressWriter, "Fetching latest changes again from '%s'...\n", opt.Remote)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to write progress")
			}
		}
		remoteStatus, err := s.ListChanges(ctx, &changesyncer.ListChangesOpt{
			RepoPath: opt.RepoPath,
			Remote:   opt.Remote,
			Changes:  opt.Changes,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to list remote changes")
		}
		// prepare change updates
		result.ChangeUpdates = make(map[changemodel.ChangeID]*changesyncer.ChangeUpdate, len(opt.Changes))
		for _, remoteChange := range remoteStatus.Changes {
			result.ChangeUpdates[remoteChange.ID] = &changesyncer.ChangeUpdate{
				CustomAttributes: remoteChange.CustomAttributes,
			}
		}
	}

	if lo.Contains(opt.Options, "branch") {
		if opt.ProgressWriter != nil {
			_, err := fmt.Fprintf(opt.ProgressWriter, "Updating remote branches...\n")
			if err != nil {
				return nil, errors.WithMessage(err, "failed to write progress")
			}
		}
		push := &git.PushOpt{
			RepoPath:       opt.RepoPath,
			Remote:         opt.Remote,
			ProgressWriter: opt.ProgressWriter,
		}
		for _, change := range changes {
			branchName := change.DefaultLocalBranchName()
			if change.LocalBranchName != nil {
				branchName = *change.LocalBranchName
			}
			push.RefSpecs = append(push.RefSpecs, fmt.Sprintf("+%s:refs/heads/%s", change.LatestVersion().CommitID, branchName))
		}
		err := s.Git.Push(ctx, push)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to update remote branches")
		}
	}

	return result, nil
}

func (s *ChangeSyncer) ListChanges(ctx context.Context, opt *changesyncer.ListChangesOpt) (*changesyncer.ListChangesResult, error) {
	// auto login
	cli, err := s.autoLogin(ctx, opt.Remote)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to auto login")
	}
	// get remote
	remote, err := s.Git.GetRemote(ctx, opt.RepoPath, opt.Remote)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get remote")
	}
	// parse repo path
	repoPath, err := remote.MainRepoPath()
	if err != nil {
		return nil, errors.WithMessage(err, "failed to extract main repo path")
	}
	// get repo
	repoResp, err := cli.GetRepository(ctx, &repository.GetRepositoryRequest{
		Path: &repoPath,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get repo by path")
	}
	repo := repoResp.Repository
	// get merge requests
	var mrs []*merge_request.MergeRequest
	if len(opt.Changes) != 0 {
		// get all mrs including dependency by local changes
		mrs, err = s.listAllMergeRequestsIncludingDependency(ctx, cli, repo.Id, opt.Changes)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to list merge requests by change ids")
		}
	} else if opt.CommitID != nil {
		// get all mrs including dependency by commit id
		mrs, err = s.listMergeRequestsIncludingDependencyByCommitID(ctx, cli, repo.Id, *opt.CommitID)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to list merge requests by commit id")
		}
	} else if opt.ID != nil {
		// get all mrs including dependency by change id
		mrs, err = s.listMergeRequestsIncludingDependencyByChangeID(ctx, cli, repo.Id, *opt.ID)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to list merge requests by commit id")
		}
	}
	// build result
	result := new(changesyncer.ListChangesResult)
	// transfer mr to change
	changes, err := mrsToChanges(mrs)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to transfer merge requests to changes")
	}
	result.Changes = changes
	// calc merged mrs
	for _, mr := range mrs {
		if mr.Status == merge_request.StatusMerged && mr.ChangeId != nil {
			result.SafeRemoveAfterRestack = append(result.SafeRemoveAfterRestack, changemodel.ChangeID(*mr.ChangeId))
		}
	}
	return result, nil
}

func (s *ChangeSyncer) listAllMergeRequestsIncludingDependency(ctx context.Context, cli *vecode.Client, repoID string, changes []*changemodel.Change) ([]*merge_request.MergeRequest, error) {
	if len(changes) == 0 {
		return nil, nil
	}
	changeIDs := lo.Map(changes, func(item *changemodel.Change, _ int) string {
		return string(item.ID)
	})
	nextIDs := make([]string, 0)
	mrsByID := make(map[string]*merge_request.MergeRequest)
	for {
		var err error
		thisMRsByID := make(map[string]*merge_request.MergeRequest)
		if len(changeIDs) > 0 {
			thisMRsByID, err = s.mGetMergeRequests(ctx, cli, repoID, changeIDs, nil)
			if err != nil {
				return nil, err
			}
			changeIDs = nil
		}
		if len(nextIDs) > 0 {
			for _, id := range nextIDs {
				mrsByID[id] = nil
			}
			thisMRsByID, err = s.mGetMergeRequests(ctx, cli, repoID, nil, nextIDs)
			if err != nil {
				return nil, err
			}
		}
		for id, mr := range thisMRsByID {
			mrsByID[id] = mr
		}
		nextIDs = nil
		for _, mr := range thisMRsByID {
			if mr == nil {
				continue
			}
			if mr.ParentMergeRequestId == nil {
				continue
			}
			if mr.Status == merge_request.StatusMerged {
				continue
			}
			if _, ok := mrsByID[*mr.ParentMergeRequestId]; !ok {
				nextIDs = append(nextIDs, *mr.ParentMergeRequestId)
			}
		}
		if len(nextIDs) == 0 {
			break
		}
	}
	return lo.WithoutEmpty(lo.Values(mrsByID)), nil
}

func (s *ChangeSyncer) mGetMergeRequests(ctx context.Context, cli *vecode.Client, repoID string, changeIDs, ids []string) (map[string]*merge_request.MergeRequest, error) {
	wg := sync.WaitGroup{}
	reqs := make([]*merge_request.GetMergeRequestRequest, 0)
	if len(changeIDs) > 0 {
		for _, cid := range changeIDs {
			reqs = append(reqs, &merge_request.GetMergeRequestRequest{
				RepoId:   repoID,
				ChangeId: pointer.To(cid),
				Selector: &merge_request.MergeRequestSelector{
					Version: pointer.To(true),
					URL:     pointer.To(true),
				},
			})
		}
	} else if len(ids) > 0 {
		for _, id := range ids {
			reqs = append(reqs, &merge_request.GetMergeRequestRequest{
				RepoId: repoID,
				Id:     pointer.To(id),
				Selector: &merge_request.MergeRequestSelector{
					Version: pointer.To(true),
					URL:     pointer.To(true),
				},
			})
		}
	} else {
		return nil, nil
	}
	mrs := make([]*merge_request.MergeRequest, len(reqs))
	errs := make([]error, len(reqs))
	for i, req := range reqs {
		i := i
		req := req
		wg.Add(1)
		go func() {
			defer wg.Done()
			resp, err := cli.GetMergeRequest(ctx, req)
			if e := new(client.ErrorResponse); errors.As(err, &e) &&
				e.ResponseMetaData != nil &&
				e.ResponseMetaData.Error != nil &&
				strings.HasPrefix(e.ResponseMetaData.Error.Code, "NotFound") {
				return
			}
			if err != nil {
				errs[i] = err
			}
			mrs[i] = resp.MergeRequest
		}()
	}
	wg.Wait()
	err := multierr.Combine(errs...)
	if err != nil {
		return nil, err
	}
	return lo.KeyBy(lo.WithoutEmpty(mrs), func(item *merge_request.MergeRequest) string {
		return item.Id
	}), nil
}

func (s *ChangeSyncer) listMergeRequestsIncludingDependencyByCommitID(ctx context.Context, cli *vecode.Client, repoID, commitID string) ([]*merge_request.MergeRequest, error) {
	selector := &merge_request.MergeRequestSelector{
		Version: pointer.To(true),
		URL:     pointer.To(true),
	}
	// list mrs by commit id
	resp, err := cli.ListRepoMergeRequests(ctx, &merge_request.ListRepoMergeRequestsRequest{
		TargetRepoId: repoID,
		CommitId:     &commitID,
		Selector:     selector,
		PageNumber:   1,
		PageSize:     100,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list repo merge requests")
	}
	// find mr by latest commit id and change mode
	mr, ok := lo.Find(resp.MergeRequests, func(item *merge_request.MergeRequest) bool {
		return item.ChangeMode == merge_request.ChangeModeMultiCommits &&
			lo.Must(lo.Last(item.Versions)).SourceCommitId == commitID
	})
	if !ok {
		return nil, nil
	}
	if mr.ParentMergeRequestId == nil {
		return []*merge_request.MergeRequest{mr}, nil
	}
	// list stack merge requests
	resp1, err := cli.ListStackMergeRequests(ctx, &merge_request.ListStackMergeRequestsRequest{
		TargetRepoId: repoID,
		Number:       &mr.Number,
		Selector:     selector,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list stack merge requests")
	}
	return resp1.MergeRequests, nil
}

func (s *ChangeSyncer) listMergeRequestsIncludingDependencyByChangeID(ctx context.Context, cli *vecode.Client, repoID string, changeID changemodel.ChangeID) ([]*merge_request.MergeRequest, error) {
	selector := &merge_request.MergeRequestSelector{
		Version: pointer.To(true),
		URL:     pointer.To(true),
	}
	// get mr by change id
	resp, err := cli.GetMergeRequest(ctx, &merge_request.GetMergeRequestRequest{
		RepoId:   repoID,
		ChangeId: pointer.To(string(changeID)),
		Selector: selector,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list repo merge requests")
	}
	mr := resp.MergeRequest
	if mr.ParentMergeRequestId == nil {
		return []*merge_request.MergeRequest{mr}, nil
	}
	// list stack merge requests
	resp1, err := cli.ListStackMergeRequests(ctx, &merge_request.ListStackMergeRequestsRequest{
		TargetRepoId: repoID,
		Number:       &mr.Number,
		Selector:     selector,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list stack merge requests")
	}
	return resp1.MergeRequests, nil
}

func (s *ChangeSyncer) autoLogin(ctx context.Context, remote string) (*vecode.Client, error) {
	// get login info
	loginInfo, err := s.Authenticator.GetLoginInfo(ctx, remote)
	if errors.Is(err, code.ErrNotLoggedIn) {
		loginInfo, err = s.Authenticator.Login(ctx, remote)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to login")
		}
	} else if err != nil {
		return nil, errors.WithMessage(err, "failed to get login info")
	}
	// get vecode client
	cli := code.NewClient(loginInfo.APIEndpoint, loginInfo.JWTToken)
	return cli, nil
}

func mrsToChanges(mrs []*merge_request.MergeRequest) ([]*changemodel.Change, error) {
	mrsByID := lo.KeyBy(mrs, func(item *merge_request.MergeRequest) string {
		return item.Id
	})
	result := make([]*changemodel.Change, len(mrs))
	for i, mr := range mrs {
		// calc parent id
		var parentID *string
		if mr.ParentMergeRequestId != nil {
			parentMR := mrsByID[*mr.ParentMergeRequestId]
			if parentMR == nil && mr.Status != merge_request.StatusMerged {
				return nil, errors.Errorf("parent merge request of %s not found", mr.Id)
			}
			if parentMR != nil && parentMR.Status != merge_request.StatusMerged {
				parentID = parentMR.ChangeId
			}
		}

		// calc created at
		createdAt, err := time.Parse(time.RFC3339, mr.CreatedAt)
		if err != nil {
			createdAt = time.Now()
		}

		// validate versions
		if len(mr.Versions) == 0 {
			return nil, errors.New("mr has no version")
		}

		// calc target branch name
		var targetBranchName *string
		if parentID == nil {
			targetBranchName = &mr.TargetBranchName
		}

		// calc change
		change := &changemodel.Change{
			ID:               changemodel.ChangeID(*mr.ChangeId),
			ParentID:         (*changemodel.ChangeID)(parentID),
			CreatedAt:        createdAt,
			TargetBranchName: targetBranchName,
			Title:            mr.Title,
			Versions: lo.Map(mr.Versions, func(item *merge_request.MergeRequestVersion, _ int) *changemodel.ChangeVersion {
				return &changemodel.ChangeVersion{
					CommitID:     item.SourceCommitId,
					BaseCommitID: item.BaseCommitId,
				}
			}),
			CustomAttributes: []changemodel.CustomAttribute{
				{
					Name:        "code.url",
					DisplayName: pointer.To("Merge Request"),
					Value:       mr.URL + "?nextui=1",
				},
			},
		}
		result[i] = change
	}
	return result, nil
}
