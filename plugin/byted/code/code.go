package code

import (
	"context"

	"code.byted.org/vecode/sdk/client"
	"code.byted.org/vecode/sdk/service/vecode"
	"github.com/pkg/errors"
)

var ErrNotLoggedIn = errors.New("not logged in")

type Authenticator interface {
	Login(ctx context.Context, remoteName string) (*LoginInfo, error)
	Logout(ctx context.Context, remoteName string) error
	GetLoginInfo(ctx context.Context, remoteName string) (*LoginInfo, error)
}

type LoginInfo struct {
	APIEndpoint string
	JWTToken    string
}

func NewClient(endpoint, jwt string) *vecode.Client {
	return vecode.NewClient(&client.Client{
		Endpoint: endpoint,
		Credentials: client.Credentials{
			UserJWT: jwt,
		},
	})
}
