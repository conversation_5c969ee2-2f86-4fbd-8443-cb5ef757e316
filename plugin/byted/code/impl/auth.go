package impl

import (
	"bytes"
	"context"
	"os/exec"
	"regexp"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	giturls "github.com/whilp/git-urls"

	"code.byted.org/vecode/staircase/plugin/byted/code"
	"code.byted.org/vecode/staircase/util/git"
)

const authenticatePath = "builtin/cli-authenticate"

var (
	endpointRegex = regexp.MustCompile(`X-Code-API-Endpoint\s+(\S+)`)
	jwtRegex      = regexp.MustCompile(`X-Code-JWT\s+(\S+)`)
)

type Authenticator struct {
	Git git.Git
}

func (c *Authenticator) Login(ctx context.Context, remoteName string) (*code.LoginInfo, error) {
	// Get remote
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return nil, errors.WithMessage(err, "not inside a git work tree")
	}
	remote, err := c.Git.GetRemote(ctx, workTree, remoteName)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get remote")
	}

	// Get info from authenticate path
	authenticateURL, err := replaceRemoteRepoPath(remote.URL, authenticatePath)
	if err != nil {
		return nil, err
	}
	args := []string{"fetch", authenticateURL}
	gitCMD := exec.CommandContext(ctx, "git", args...)
	outputBytes, _ := gitCMD.CombinedOutput() // Get from stderr
	output := string(bytes.TrimSpace(outputBytes))

	// Extract login info
	endpointMatches := endpointRegex.FindStringSubmatch(output)
	jwtMatches := jwtRegex.FindStringSubmatch(output)
	if len(endpointMatches) != 2 || len(jwtMatches) != 2 {
		return nil, errors.Errorf("unexpected output from authenticate path. output: %s", output)
	}

	apiEndpoint := endpointMatches[1]
	jwtToken := jwtMatches[1]

	// Update config
	err = c.Git.UpdateRepoConfig(ctx, workTree, getAPIEndpointConfigKey(remoteName), apiEndpoint)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update git config")
	}
	err = c.Git.UpdateRepoConfig(ctx, workTree, getJWTConfigKey(remoteName), jwtToken)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update git config")
	}

	return &code.LoginInfo{
		APIEndpoint: apiEndpoint,
		JWTToken:    jwtToken,
	}, nil
}

func replaceRemoteRepoPath(remoteURL string, repoPath string) (string, error) {
	validSchemes := []string{"https", "http", "ssh"}
	url, err := giturls.Parse(remoteURL)
	if err != nil || url == nil {
		return "", errors.WithMessage(err, "failed to parse remote url")
	}
	if !lo.Contains(validSchemes, url.Scheme) {
		return "", errors.Errorf("unknown remote url: %s", remoteURL)
	}

	url.Path = repoPath
	return url.String(), nil
}

func (c *Authenticator) Logout(ctx context.Context, remoteName string) error {
	// Get work tree
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}

	// Unset config
	err = c.Git.UnsetRepoConfig(ctx, workTree, getAPIEndpointConfigKey(remoteName))
	if err != nil {
		return errors.WithMessage(err, "failed to unset git config")
	}
	err = c.Git.UnsetRepoConfig(ctx, workTree, getJWTConfigKey(remoteName))
	if err != nil {
		return errors.WithMessage(err, "failed to unset git config")
	}

	return nil
}

func (c *Authenticator) GetLoginInfo(ctx context.Context, remoteName string) (*code.LoginInfo, error) {
	// Get work tree
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return nil, errors.WithMessage(err, "not inside a git work tree")
	}

	// Get config
	apiEndpoint, err := c.Git.GetRepoConfig(ctx, workTree, getAPIEndpointConfigKey(remoteName))
	if errors.Is(err, git.ErrConfigNotFound) {
		return nil, code.ErrNotLoggedIn
	}
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get git config")
	}
	jwtToken, err := c.Git.GetRepoConfig(ctx, workTree, getJWTConfigKey(remoteName))
	if errors.Is(err, git.ErrConfigNotFound) {
		return nil, code.ErrNotLoggedIn
	}
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get git config")
	}

	// parse jwt
	var claims jwt.RegisteredClaims
	_, _, err = jwt.NewParser().ParseUnverified(jwtToken, &claims)
	if err != nil {
		return nil, code.ErrNotLoggedIn
	}
	if claims.ExpiresAt != nil && time.Now().Add(time.Hour).After(claims.ExpiresAt.Time) {
		return nil, code.ErrNotLoggedIn
	}

	return &code.LoginInfo{
		APIEndpoint: apiEndpoint,
		JWTToken:    jwtToken,
	}, nil
}
