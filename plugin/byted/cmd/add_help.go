package cmd

import (
	"strings"

	"github.com/charmbracelet/lipgloss"
	"github.com/spf13/cobra"
	"go.uber.org/fx"

	cmdutil "code.byted.org/vecode/staircase/util/cmd"
)

func init() {
	Options = append(Options, fx.Invoke(AddHelp))
}

func AddHelp(root *cobra.Command) {
	examplesByCommandName := map[string]string{
		"":         "For overview, see also https://bytedance.larkoffice.com/wiki/HliwwUvW2idscLkVXh9cKZ5Nnlx",
		"abort":    "See also https://bytedance.larkoffice.com/wiki/DdBzw1zGLipwZ8kfzKJcZeiVned",
		"absorb":   "See also https://bytedance.larkoffice.com/wiki/M2IZw278ZiCRkvkm5pPcsaMbn0K",
		"continue": "see also https://bytedance.larkoffice.com/wiki/DdBzw1zGLipwZ8kfzKJcZeiVned",
		"rebase": `See also https://bytedance.larkoffice.com/wiki/YkHIwB2j8iin6FkYvJbcj1fUnUe
For conflict resolution, see https://bytedance.larkoffice.com/wiki/DdBzw1zGLipwZ8kfzKJcZeiVned`,
		"redo": "See also https://bytedance.larkoffice.com/wiki/AS4xwFq7tiRAZZk7FfDczrOknUd",
		"restack": `See also https://bytedance.larkoffice.com/wiki/YkHIwB2j8iin6FkYvJbcj1fUnUe
For conflict resolution, see https://bytedance.larkoffice.com/wiki/DdBzw1zGLipwZ8kfzKJcZeiVned`,
		"split":  `See also https://bytedance.larkoffice.com/wiki/B4cRwpvkyicFFlkyRhNc42FTnTd`,
		"submit": `For collaborating using st, see https://bytedance.larkoffice.com/wiki/DJbxw1dvmikJhJkVYYecv9D2nZd`,
		"sync":   `For collaborating using st, see https://bytedance.larkoffice.com/wiki/DJbxw1dvmikJhJkVYYecv9D2nZd`,
		"undo":   "See also https://bytedance.larkoffice.com/wiki/AS4xwFq7tiRAZZk7FfDczrOknUd",
	}
	for name, example := range examplesByCommandName {
		cmd, _, err := root.Find([]string{name})
		if err != nil {
			continue
		}
		example = lipgloss.NewStyle().PaddingLeft(2).Render(example)
		if cmd.Example == "" {
			cmd.Example = example
			continue
		}
		if !strings.HasSuffix(cmd.Example, "\n") {
			cmd.Example += "\n"
		}
		cmd.Example += example
	}

	submitCmd, _, err := root.Find([]string{"submit"})
	if err == nil {
		cmdutil.AddAdditionalHelp(submitCmd, cmdutil.RenderHelp(`You can use '-o draft' to mark merge requests to be created as draft.

You can use '-o branch' to push local branches to remote.

If a downstack change is already merged in remote, you need to run 'st sync' before 'st submit'.

If a downstack change is closed in remote, you need to either reopen that change or rebase changes relying on that closed change to another place before submit.`))
	}
}
