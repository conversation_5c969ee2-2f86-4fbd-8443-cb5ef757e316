package cmd

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path"
	"reflect"
	"runtime"
	"strings"
	"time"

	"github.com/AlekSi/pointer"
	"github.com/Masterminds/semver/v3"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/go-resty/resty/v2"
	"github.com/spf13/cobra"
	"go.uber.org/fx"

	"code.byted.org/vecode/staircase/cmd/bubbles"
	"code.byted.org/vecode/staircase/util/build"
)

func init() {
	Options = append(Options, fx.Invoke(InitAutoUpgrade))
}

func InitAutoUpgrade(root *cobra.Command) {
	oldPersistentPreRun := root.PersistentPreRun
	root.PersistentPreRun = func(cmd *cobra.Command, args []string) {
		if ShouldRunAutoUpgrade(cmd) {
			AutoUpgrade(cmd, args)
		}
		if oldPersistentPreRun != nil {
			oldPersistentPreRun(cmd, args)
		}
	}
}

func ShouldRunAutoUpgrade(cmd *cobra.Command) bool {
	// only run auto upgrade when executing commands added by us
	if cmd.Hidden {
		return false
	}
	var f *reflect.Value
	if cmd.Run != nil {
		f = pointer.To(reflect.ValueOf(cmd.Run))
	} else if cmd.RunE != nil {
		f = pointer.To(reflect.ValueOf(cmd.RunE))
	} else {
		return false
	}
	fn := runtime.FuncForPC(f.Pointer())
	if fn == nil {
		return false
	}
	return strings.Contains(fn.Name(), "staircase")
}

func AutoUpgrade(cmd *cobra.Command, args []string) {
	ctx := cmd.Context()
	// check update time
	infoDir := path.Join(os.TempDir(), "staircase")
	infoPath := path.Join(infoDir, "last_upgrade_checked_at")
	rawInfo, _ := os.ReadFile(infoPath)
	var lastUpgradeCheckedAt *time.Time
	if t, err := time.Parse(time.RFC3339, string(rawInfo)); err == nil {
		lastUpgradeCheckedAt = &t
	}
	now := time.Now()
	if lastUpgradeCheckedAt != nil && lastUpgradeCheckedAt.Add(time.Hour*24).After(now) {
		return
	}
	_ = os.MkdirAll(infoDir, os.ModePerm)
	_ = os.WriteFile(infoPath, []byte(now.Format(time.RFC3339)), os.ModePerm)
	// check for update
	println("Checking for updates...")
	// get latest version
	var latestVersion *semver.Version
	{
		ctx, cancel := context.WithTimeout(cmd.Context(), 5*time.Second)
		defer cancel()
		r, err := resty.New().R().SetContext(ctx).Get("https://wjjb779n.fn.bytedance.net/download/latest_version.txt")
		if err != nil {
			return
		}
		if r.IsError() {
			return
		}
		latestVersion, err = semver.NewVersion(string(r.Body()))
		if err != nil {
			return
		}
	}
	currentVersion, err := semver.NewVersion(build.Version)
	if err != nil {
		return
	}
	if !currentVersion.LessThan(latestVersion) {
		return
	}
	// prompt user for upgrade confirmation
	m := &bubbles.Confirmation{
		Title:         fmt.Sprintf("Current version %s is outdated, upgrade to %s?(Y/n)", currentVersion.Original(), latestVersion.Original()),
		DefaultChoice: true,
	}
	mi, err := tea.NewProgram(m).Run()
	if err != nil {
		return
	}
	m = mi.(*bubbles.Confirmation)

	if !m.Confirmed() {
		os.Exit(0)
		return
	}

	// perform upgrade
	if runtime.GOOS == "darwin" {
		cmd := exec.CommandContext(ctx, "sh", "-c", "brew update && brew upgrade staircase")
		cmd.Stdin = os.Stdin
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
		err := cmd.Run()
		if err == nil {
			os.Exit(0)
			return
		}
	}
	println("Auto upgrade failed, please visit https://bytedance.larkoffice.com/wiki/HNmFwfxyViF5N2klWkicY9Amn4e for more information.")
	os.Exit(0)
}
