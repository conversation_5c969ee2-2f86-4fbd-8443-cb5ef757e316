package cmd

import (
	"github.com/spf13/cobra"
	"go.uber.org/fx"
)

func init() {
	Options = append(Options, fx.Supply(fx.Annotated{
		Name: "submit_option",
		Target: func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
			completions := []string{
				"draft\tMark merge requests to be created as draft",
				"branch\tPush local branches to remote",
			}
			return completions, cobra.ShellCompDirectiveNoFileComp
		},
	}))
}
