//go:build tool

package cmd

import (
	"bytes"
	"fmt"
	"os"

	"github.com/pkg/errors"
	"github.com/spf13/cobra"
	"go.uber.org/fx"

	changedal "code.byted.org/vecode/staircase/module/change/dal"
	changeservice "code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/util/cmd"
	cmdutil "code.byted.org/vecode/staircase/util/cmd"
	"code.byted.org/vecode/staircase/util/fxutil"
	"code.byted.org/vecode/staircase/util/git"
)

func init() {
	Options = append(Options,
		fx.Provide(fxutil.StructConstructor(new(GenFeishuMarkdownCommand))),
		fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(GenFeishuMarkdownCommand), new(cmd.Registrant)))),
	)
}

type GenFeishuMarkdownCommand struct {
	Git                   git.Git
	ChangeDAO             changedal.ChangeDAO
	RefreshChangesService changeservice.RefreshChangesService
}

func (c *GenFeishuMarkdownCommand) Run(cmd *cobra.Command, _ []string) error {
	root := cmd.Parent()
	buf := &bytes.Buffer{}
	buf.WriteString("# Available Commands\n")
	printFeishuMarkdown(root, buf)
	_, err := os.Stdout.WriteString(buf.String())
	if err != nil {
		return errors.WithMessage(err, "failed to write markdown")
	}
	return nil
}

func (c *GenFeishuMarkdownCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:    "gen-feishu-markdown",
		RunE:   c.Run,
		Hidden: true,
	}
	command.AddCommand(cmd)
	return nil
}

func printFeishuMarkdown(cmd *cobra.Command, buf *bytes.Buffer) {
	if !cmd.IsAvailableCommand() || cmd.IsAdditionalHelpTopicCommand() {
		return
	}

	name := cmd.CommandPath()

	buf.WriteString("## " + name + "\n")
	buf.WriteString(cmd.Short + "\n")
	if len(cmd.Long) > 0 {
		buf.WriteString("**Synopsis**\n")
		buf.WriteString(fmt.Sprintf("```\n%s\n```\n", cmd.Long))
	}

	if cmd.Runnable() {
		buf.WriteString("**Usage**\n")
		buf.WriteString(fmt.Sprintf("```\n%s\n```\n", cmd.UseLine()))
	}

	if len(cmd.Example) > 0 {
		buf.WriteString("**Examples**\n")
		buf.WriteString(fmt.Sprintf("```\n%s\n```\n", cmd.Example))
	}

	if additionalHelp := cmdutil.GetAdditionalHelp(cmd); additionalHelp != "" {
		buf.WriteString("**Additional Help**\n")
		buf.WriteString(fmt.Sprintf("```\n%s\n```\n", additionalHelp))
	}

	printOptions(buf, cmd)

	for _, child := range cmd.Commands() {
		printFeishuMarkdown(child, buf)
	}
}

func printOptions(buf *bytes.Buffer, cmd *cobra.Command) {
	flags := cmd.NonInheritedFlags()
	flags.SetOutput(buf)
	if flags.HasAvailableFlags() {
		buf.WriteString("**Options**\n```\n")
		flags.PrintDefaults()
		buf.WriteString("```\n")
	}

	parentFlags := cmd.InheritedFlags()
	parentFlags.SetOutput(buf)
	if parentFlags.HasAvailableFlags() {
		buf.WriteString("**Options inherited from parent commands**\n```\n")
		parentFlags.PrintDefaults()
		buf.WriteString("```\n")
	}
}
