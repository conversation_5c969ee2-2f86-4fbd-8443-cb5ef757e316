# yaml-language-server: $schema=https://goreleaser.com/static/schema.json
# vim: set ts=2 sw=2 tw=0 fo=cnqoj

version: 1

before:
  hooks:
    - rm -rf output
    - mkdir -p output
    - sh -c "go run main.go completion zsh > output/completion.zsh"
    - sh -c "go run main.go completion bash > output/completion.bash"
    - sh -c "go run main.go completion fish > output/completion.fish"
builds:
  - env:
      - CGO_ENABLED=0
    goos:
      - darwin
    ldflags:
      - >-
        -s
        -w
        -X code.byted.org/vecode/staircase/util/build.Version={{.Version}}
        -X code.byted.org/vecode/staircase/util/build.Date={{.Date}}
        -X code.byted.org/vecode/staircase/util/build.CommitID={{.FullCommit}}
        -X code.byted.org/vecode/staircase/util/build.CommittedAt={{.CommitDate}}

archives:
  - format: tar.gz
    # this name template makes the OS and Arch compatible with the results of `uname`.
    name_template: '{{ .ProjectName }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}'
    files:
      - src: output/completion.zsh
        dst: completion.zsh
      - src: output/completion.bash
        dst: completion.bash
      - src: output/completion.fish
        dst: completion.fish
    # use zip for windows archives
    format_overrides:
      - goos: windows
        format: zip

changelog:
  sort: asc
  filters:
    exclude:
      - "^docs:"
      - "^test:"

release:
  disable: true

publishers:
  - cmd: >- 
      curl -X PUT
      -H "Content-Type: application/octet-stream"
      -H "Access-Key: {{ .Env.TOSAccessKey }}"
      https://wjjb779n.fn.bytedance.net/upload/{{ .ArtifactName }} --data-binary @{{ .ArtifactPath }}

brews:
  - name: staircase
    dependencies:
      - name: git
        version: v2.43.0
      - name: git-absorb
        version: v0.6.13
    url_template: "https://wjjb779n.fn.bytedance.net/download/{{ .ArtifactName }}"
    install: |
      bin.install "staircase" => "st"
      bash_completion.install "completion.bash" => "st"
      fish_completion.install "completion.fish" => "st.fish"
      zsh_completion.install "completion.zsh" => "_st"
    repository:
      owner: "staircase"
      name: "tap"
      git:
        url: "https://code.byted.org/staircase/tap"
        private_key: '{{ .Env.PRIVATE_KEY_PATH }}'

announce:
  skip: true