export SHELL := /bin/bash
COVER_PKGS = code.byted.org/vecode/staircae/module/...
LINT_FILES = $$(find . -type f -name '*.go' -not -path "*_gen*" -not -path "./submodule/*" -not -path "./.git/*")

.PHONY: lint
lint:
	gofmt -w $(LINT_FILES)
	go mod tidy
	go run golang.org/x/tools/cmd/goimports -local code.byted.org/vecode/staircase -w $(LINT_FILES)
	go run mvdan.cc/gofumpt -l -w $(LINT_FILES)

.PHONY: test
test:
	go test -race -coverpkg=$(COVER_PKGS) -coverprofile=coverage.out ./... -gcflags all=-l

.PHONY: gen
gen:
	find . -type f -name "*_gen.go" | xargs rm
	go generate ./...
	make lint

.PHONY: build
build:
	rm -rf output
	mkdir -p output
	go build -o output/st && chmod +x output/st

.PHONY: release
release:
	go run tool/release/main.go -release-type "$(release_type)"

# bytedance targets
.PHONY: gen-feishu-markdown
gen-feishu-markdown:
	@go run -tags tool main.go gen-feishu-markdown
