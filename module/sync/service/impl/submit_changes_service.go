package impl

import (
	"context"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	changedal "code.byted.org/vecode/staircase/module/change/dal"
	changemodel "code.byted.org/vecode/staircase/module/change/model"
	changeservice "code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/module/sync/changesyncer"
	"code.byted.org/vecode/staircase/module/sync/service"
	"code.byted.org/vecode/staircase/util/git"
)

type SubmitChangesService struct {
	ListChangesService changeservice.ListChangesService
	DAO                changedal.ChangeDAO
	Git                git.Git
	ChangeSyncer       changesyncer.ChangeSyncer
}

func (s *SubmitChangesService) Do(ctx context.Context, opt *service.SubmitChangesOpt) error {
	items, err := s.ListChangesService.Do(ctx, &changeservice.ListChangesOpt{
		RepoPath:      opt.RepoPath,
		DownStackFrom: opt.Change,
	})
	if err != nil {
		return errors.WithMessage(err, "list down-stack changes failed")
	}

	// submit changes
	result, err := s.ChangeSyncer.SyncChanges(ctx, &changesyncer.SyncChangesOpt{
		RepoPath:       opt.RepoPath,
		Remote:         opt.Remote,
		Changes:        items.Changes(),
		Options:        opt.Options,
		ProgressWriter: opt.ProgressWriter,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to sync changes")
	}

	// apply updates
	if len(result.ChangeUpdates) != 0 {
		changes, err := s.DAO.ListAllChanges(ctx, opt.RepoPath)
		if err != nil {
			return errors.WithMessage(err, "failed to list all changes")
		}
		changesByID := lo.KeyBy(changes, func(item *changemodel.Change) changemodel.ChangeID {
			return item.ID
		})
		updatedChanges := make([]*changemodel.Change, 0, len(result.ChangeUpdates))
		for id, update := range result.ChangeUpdates {
			change := changesByID[id]
			if change == nil {
				continue
			}
			for _, attribute := range update.CustomAttributes {
				change.SetCustomAttribute(attribute)
			}
			updatedChanges = append(updatedChanges, change)
		}
		_, err = s.DAO.BatchOperateChanges(ctx, opt.RepoPath, updatedChanges, nil)
		if err != nil {
			return errors.WithMessage(err, "failed to save changes")
		}
	}
	return nil
}
