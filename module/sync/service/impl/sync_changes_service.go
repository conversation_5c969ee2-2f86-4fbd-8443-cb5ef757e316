package impl

import (
	"context"
	"fmt"

	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	changemodel "code.byted.org/vecode/staircase/module/change/model"
	changeservice "code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/module/sync/changesyncer"
	"code.byted.org/vecode/staircase/module/sync/service"
	"code.byted.org/vecode/staircase/util/git"
)

type SyncChangesService struct {
	ListChangesService changeservice.ListChangesService
	ChangeSyncer       changesyncer.ChangeSyncer
	RebaseService      changeservice.RebaseService
	Git                git.Git
}

func (s *SyncChangesService) Do(ctx context.Context, opt *service.SyncChangesOpt) (*changemodel.Rebase, error) {
	// get default branch to sync
	defaultBranchName, err := s.Git.GetDefaultBranchName(ctx, opt.RepoPath, git.DefaultRemote)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get default branch")
	}
	// list all current changes
	items, err := s.ListChangesService.Do(ctx, &changeservice.ListChangesOpt{
		RepoPath: opt.RepoPath,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list changes")
	}
	changes := items.Changes()
	// calc target branch names
	targetBranchNames := lo.Uniq(lo.WithoutEmpty(lo.Map(changes, func(item *changemodel.Change, _ int) string {
		return pointer.Get(item.TargetBranchName)
	})))
	// list remote changes
	if opt.ProgressWriter != nil {
		_, err := fmt.Fprintf(opt.ProgressWriter, "Fetching latest changes from '%s'...\n", opt.Remote)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to write progress")
		}
	}
	res, err := s.ChangeSyncer.ListChanges(ctx, &changesyncer.ListChangesOpt{
		RepoPath: opt.RepoPath,
		Remote:   opt.Remote,
		Changes:  opt.Changes,
		CommitID: opt.CommitID,
		ID:       opt.ID,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list latest changes")
	}
	latestChanges := res.Changes
	// sort changes
	var list changemodel.ChangeList
	for _, change := range latestChanges {
		list = append(list, changemodel.ChangeListItem{
			Change: change,
		})
	}
	list, err = changemodel.SortChangeList(list)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to sort change list")
	}
	latestChanges = list.Changes()
	// calc latest target branches
	latestTargetBranchNames := lo.Uniq(lo.WithoutEmpty(lo.Map(latestChanges, func(item *changemodel.Change, _ int) string {
		return pointer.Get(item.TargetBranchName)
	})))
	// calc branches to sync
	branchesToSync := lo.Uniq(lo.Flatten([][]string{
		{defaultBranchName},
		targetBranchNames,
		latestTargetBranchNames,
	}))
	// get latest target branches' commit
	if opt.ProgressWriter != nil {
		_, err := fmt.Fprintf(opt.ProgressWriter, "Fetching latest branches' status from '%s'...\n", opt.Remote)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to write progress")
		}
	}
	remoteRefs, err := s.Git.ListRemoteReferences(ctx, opt.RepoPath, opt.Remote, lo.Map(branchesToSync, func(item string, _ int) string {
		return git.BranchRefPrefix + item
	})...)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list remote refs")
	}
	// exclude non-existent remote branches in branches to sync
	branchesToSync = lo.Intersect(branchesToSync, lo.Map(remoteRefs, func(item git.Reference, _ int) string {
		return item.Name.BranchName()
	}))
	// calc latest commit-ids
	latestCommitIDs := lo.Union(
		lo.Map(remoteRefs, func(item git.Reference, _ int) string {
			return item.CommitID
		}),
		lo.FlatMap(latestChanges, func(item *changemodel.Change, _ int) []string {
			return []string{
				item.LatestVersion().CommitID,
				item.LatestVersion().BaseCommitID,
			}
		}),
	)
	// fetch latest commits
	if opt.ProgressWriter != nil {
		_, err := fmt.Fprintf(opt.ProgressWriter, "Fetching latest commits from '%s'...\n", opt.Remote)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to write progress")
		}
	}
	err = s.Git.Fetch(ctx, &git.FetchOpt{
		RepoPath:       opt.RepoPath,
		Remote:         opt.Remote,
		RefSpecs:       append(branchesToSync, latestCommitIDs...),
		ProgressWriter: opt.ProgressWriter,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to fetch latest target branches and commits")
	}
	// prepare rebase actions
	rebase := new(changemodel.Rebase)
	// update local branches
	for _, branchName := range branchesToSync {
		// rebase local branch to latest remote branch
		// get local commit id
		branch, err := s.Git.GetBranch(ctx, opt.RepoPath, branchName)
		if errors.Is(err, git.ErrBranchNotFound) {
			// get remote commit id
			ref, ok := lo.Find(remoteRefs, func(item git.Reference) bool {
				return item.Name.BranchName() == branchName
			})
			if !ok {
				// not exist in remote
				continue
			}
			// add rebase action
			rebase.Actions = append(rebase.Actions, &changemodel.RebaseAction{
				RefUpdate: &changemodel.RefUpdate{
					Name: git.BranchRefPrefix + branchName,
					CommitID: changemodel.CommitIDValue{
						Value: &ref.CommitID,
					},
				},
			})
			continue
		}
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get branch(%s)", branchName)
		}
		// get remote commit id
		ref, ok := lo.Find(remoteRefs, func(item git.Reference) bool {
			return item.Name.BranchName() == branchName
		})
		if !ok {
			// not exist in remote
			continue
		}
		// add rebase action
		rebase.Actions = append(rebase.Actions, &changemodel.RebaseAction{
			Rebase: &changemodel.GitRebase{
				NewBaseCommitID: changemodel.CommitIDValue{
					Value: &ref.CommitID,
				},
				BaseCommitID: changemodel.CommitIDValue{
					Value: &ref.CommitID,
				},
				EndCommitID: changemodel.CommitIDValue{
					Value: &branch.Commit.ID,
				},
			},
			RefUpdate: &changemodel.RefUpdate{
				Name: git.BranchRefPrefix + branchName,
				CommitID: changemodel.CommitIDValue{
					RebaseCommitID: true,
				},
			},
		})
	}
	// update local changes
	for _, remoteChange := range latestChanges {
		// find local change
		localChange, ok := lo.Find(changes, func(item *changemodel.Change) bool {
			return item.ID == remoteChange.ID
		})
		if !ok {
			// new change
			rebase.Actions = append(rebase.Actions, &changemodel.RebaseAction{
				ChangeCreate: &changemodel.ChangeCreate{
					ID:               remoteChange.ID,
					ParentID:         remoteChange.ParentID,
					TargetBranchName: remoteChange.TargetBranchName,
					Title:            remoteChange.Title,
					CustomAttributes: remoteChange.CustomAttributes,
					BaseCommitID:     remoteChange.LatestVersion().BaseCommitID,
					CommitID:         remoteChange.LatestVersion().CommitID,
				},
			})
			continue
		}
		// calculate rebase action for existing change
		var action *changemodel.RebaseAction
		localChangeOutdated := lo.SomeBy(remoteChange.Versions, func(item *changemodel.ChangeVersion) bool {
			return item.CommitID == localChange.LatestVersion().CommitID &&
				item.BaseCommitID == localChange.LatestVersion().BaseCommitID
		})
		remoteChangeOutdated := lo.SomeBy(localChange.Versions, func(item *changemodel.ChangeVersion) bool {
			return item.CommitID == remoteChange.LatestVersion().CommitID &&
				item.BaseCommitID == remoteChange.LatestVersion().BaseCommitID
		})
		if localChangeOutdated {
			var refUpdate *changemodel.RefUpdate
			if localChange.LocalBranchName != nil {
				refUpdate = &changemodel.RefUpdate{
					Name: git.BranchRefPrefix + *localChange.LocalBranchName,
					CommitID: changemodel.CommitIDValue{
						Value: &remoteChange.LatestVersion().CommitID,
					},
				}
			}
			// update current change directly
			action = &changemodel.RebaseAction{
				RefUpdate: refUpdate,
				ChangeUpdate: &changemodel.ChangeUpdate{
					ID:               remoteChange.ID,
					Title:            &remoteChange.Title,
					CustomAttributes: remoteChange.CustomAttributes,
					ParentID: &changemodel.Value[changemodel.ChangeID]{
						Value: remoteChange.ParentID,
					},
					TargetBranchName: &changemodel.Value[string]{
						Value: remoteChange.TargetBranchName,
					},
					CommitID: &changemodel.CommitIDValue{
						Value: &remoteChange.LatestVersion().CommitID,
					},
					BaseCommitID: &changemodel.CommitIDValue{
						Value: &remoteChange.LatestVersion().BaseCommitID,
					},
				},
			}
		} else if remoteChangeOutdated {
			// only update metadata
			action = &changemodel.RebaseAction{
				ChangeUpdate: &changemodel.ChangeUpdate{
					ID:               remoteChange.ID,
					Title:            &remoteChange.Title,
					CustomAttributes: remoteChange.CustomAttributes,
					ParentID: &changemodel.Value[changemodel.ChangeID]{
						Value: remoteChange.ParentID,
					},
					TargetBranchName: &changemodel.Value[string]{
						Value: remoteChange.TargetBranchName,
					},
				},
			}
		} else { // remote and current change have diverged commits
			// rebase local change to remote change's commit
			var refUpdate *changemodel.RefUpdate
			if localChange.LocalBranchName != nil {
				refUpdate = &changemodel.RefUpdate{
					Name: git.BranchRefPrefix + *localChange.LocalBranchName,
					CommitID: changemodel.CommitIDValue{
						RebaseCommitID: true,
					},
				}
			}
			action = &changemodel.RebaseAction{
				// rebase local change to remote change's commit
				Rebase: &changemodel.GitRebase{
					NewBaseCommitID: changemodel.CommitIDValue{
						Value: &remoteChange.LatestVersion().CommitID,
					},
					BaseCommitID: changemodel.CommitIDValue{
						Value: &localChange.LatestVersion().BaseCommitID,
					},
					EndCommitID: changemodel.CommitIDValue{
						Value: &localChange.LatestVersion().CommitID,
					},
				},
				RefUpdate: refUpdate,
				ChangeUpdate: &changemodel.ChangeUpdate{
					ID:    remoteChange.ID,
					Title: &remoteChange.Title,
					ParentID: &changemodel.Value[changemodel.ChangeID]{
						Value: remoteChange.ParentID,
					},
					TargetBranchName: &changemodel.Value[string]{
						Value: remoteChange.TargetBranchName,
					},
					CustomAttributes: remoteChange.CustomAttributes,
					CommitID: &changemodel.CommitIDValue{
						RebaseCommitID: true,
					},
					// set local change's base commit to remote change's base commit
					BaseCommitID: &changemodel.CommitIDValue{
						Value: &remoteChange.LatestVersion().BaseCommitID,
					},
				},
			}
		}
		rebase.Actions = append(rebase.Actions, action)
	}
	// rebase all changes
	if opt.Restack {
		g := (*changemodel.ChangeGraph)(&changes)
		err := g.Update(latestChanges)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to update changes")
		}
		actions := changemodel.NewRebaseActionsFromChanges(changes, false)
		rebase.Actions = append(rebase.Actions, actions...)
		rebase.SafeRemove = res.SafeRemoveAfterRestack
	}
	return rebase, nil
}
