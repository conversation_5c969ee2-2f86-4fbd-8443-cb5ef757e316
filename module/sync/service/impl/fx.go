package impl

import (
	"go.uber.org/fx"

	"code.byted.org/vecode/staircase/module/sync/service"
	"code.byted.org/vecode/staircase/util/fxutil"
)

var Module = fx.Options(
	fx.Provide(fxutil.StructConstructor(new(SubmitChangesService))),
	fx.Provide(fxutil.Bind(new(SubmitChangesService), new(service.SubmitChangesService))),
	fx.Provide(fxutil.StructConstructor(new(SyncChangesService))),
	fx.Provide(fxutil.Bind(new(SyncChangesService), new(service.SyncChangesService))),
)
