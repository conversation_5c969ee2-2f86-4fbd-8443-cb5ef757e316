package service

import (
	"context"
	"io"

	changemodel "code.byted.org/vecode/staircase/module/change/model"
)

type SubmitChangesOpt struct {
	RepoPath       string
	Change         *changemodel.Change
	Remote         string
	Options        []string
	ProgressWriter io.Writer
}

type SubmitChangesService interface {
	Do(ctx context.Context, opt *SubmitChangesOpt) error
}

type SyncChangesOpt struct {
	RepoPath       string
	Remote         string
	Changes        []*changemodel.Change
	CommitID       *string
	ID             *changemodel.ChangeID
	Restack        bool
	ProgressWriter io.Writer
}

type SyncChangesService interface {
	Do(ctx context.Context, opt *SyncChangesOpt) (*changemodel.Rebase, error)
}
