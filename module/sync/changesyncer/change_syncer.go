package changesyncer

import (
	"context"
	"io"

	changemodel "code.byted.org/vecode/staircase/module/change/model"
)

type ListChangesOpt struct {
	RepoPath string
	Remote   string
	Changes  []*changemodel.Change
	CommitID *string
	ID       *changemodel.ChangeID
}

type ListChangesResult struct {
	Changes                []*changemodel.Change
	SafeRemoveAfterRestack []changemodel.ChangeID
}

type SyncChangesOpt struct {
	RepoPath       string
	Remote         string
	Changes        []*changemodel.Change
	Options        []string
	ProgressWriter io.Writer
}

type ChangeSyncer interface {
	// ListChanges should return latest changes including dependency for provided changes
	ListChanges(ctx context.Context, opt *ListChangesOpt) (*ListChangesResult, error)
	// SyncChanges should update remote with provided changes and their relationship
	SyncChanges(ctx context.Context, opt *SyncChangesOpt) (*SyncChangesResult, error)
}

type SyncChangesResult struct {
	ChangeUpdates map[changemodel.ChangeID]*ChangeUpdate
}

type ChangeUpdate struct {
	CustomAttributes []changemodel.CustomAttribute
}
