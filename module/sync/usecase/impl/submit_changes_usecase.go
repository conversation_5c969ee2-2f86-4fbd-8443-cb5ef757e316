package impl

import (
	"context"

	"github.com/pkg/errors"

	"code.byted.org/vecode/staircase/module/change/dal"
	changeservice "code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/module/sync/service"
	"code.byted.org/vecode/staircase/module/sync/usecase"
	"code.byted.org/vecode/staircase/util/git"
)

type SubmitChangesUsecase struct {
	DAO                   dal.ChangeDAO
	Git                   git.Git
	SubmitChangesService  service.SubmitChangesService
	RefreshChangesService changeservice.RefreshChangesService
}

func (c *SubmitChangesUsecase) Do(ctx context.Context, opt *usecase.SubmitChangesOpt) error {
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// check remote
	_, err = c.Git.GetRemote(ctx, workTree, opt.Remote)
	if errors.Is(err, git.ErrRemoteNotFound) {
		return err
	}
	if err != nil {
		return errors.WithMessage(err, "failed to get remote")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &changeservice.RefreshChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessage(err, "refresh changes failed")
	}
	// get current
	branch, err := c.Git.GetCurrentBranch(ctx, workTree)
	if errors.Is(err, git.ErrBranchNotFound) {
		return errors.WithMessage(err, "not checked out out to a branch")
	} else if err != nil {
		return errors.WithMessage(err, "get current branch failed")
	}
	change, err := c.DAO.GetChangeByLocalBranch(ctx, workTree, branch)
	if errors.Is(err, dal.ErrChangeNotFound) {
		return errors.WithMessage(err, "current branch is not tracked")
	}
	// submit changes
	remote := opt.Remote
	if remote == "" {
		remote = git.DefaultRemote
	}
	err = c.SubmitChangesService.Do(ctx, &service.SubmitChangesOpt{
		RepoPath:       workTree,
		Change:         change,
		Remote:         remote,
		Options:        opt.Options,
		ProgressWriter: opt.ProgressWriter,
	})
	if err != nil {
		return errors.WithMessage(err, "submit change failed")
	}
	return nil
}
