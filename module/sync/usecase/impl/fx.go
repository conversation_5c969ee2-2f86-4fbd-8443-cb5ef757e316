package impl

import (
	"go.uber.org/fx"

	"code.byted.org/vecode/staircase/module/sync/usecase"
	"code.byted.org/vecode/staircase/util/fxutil"
)

var Module = fx.Options(
	fx.Provide(fxutil.StructConstructor(new(SubmitChangesUsecase))),
	fx.Provide(fxutil.Bind(new(SubmitChangesUsecase), new(usecase.SubmitChangesUsecase))),
	fx.Provide(fxutil.StructConstructor(new(SyncChangesUsecase))),
	fx.Provide(fxutil.Bind(new(SyncChangesUsecase), new(usecase.SyncChangesUsecase))),
)
