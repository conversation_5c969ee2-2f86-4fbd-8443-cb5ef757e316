package impl

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	changedal "code.byted.org/vecode/staircase/module/change/dal"
	changemodel "code.byted.org/vecode/staircase/module/change/model"
	changeservice "code.byted.org/vecode/staircase/module/change/service"
	changeusecase "code.byted.org/vecode/staircase/module/change/usecase"
	"code.byted.org/vecode/staircase/module/sync/service"
	"code.byted.org/vecode/staircase/module/sync/usecase"
	"code.byted.org/vecode/staircase/util/git"
)

type SyncChangesUsecase struct {
	Git                   git.Git
	SyncChangesService    service.SyncChangesService
	ChangeDAO             changedal.ChangeDAO
	ListChangesService    changeservice.ListChangesService
	RefreshChangesService changeservice.RefreshChangesService
	RebaseService         changeservice.RebaseService
	RebaseUsecase         changeusecase.RebaseUsecase
}

func (c *SyncChangesUsecase) Do(ctx context.Context, opt *usecase.SyncChangesOpt) error {
	// get current working directory
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// check worktree
	clean, err := c.Git.IsWorktreeClean(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "failed to check work tree")
	}
	if !clean {
		return errors.New("work tree not clean")
	}
	// check remote
	_, err = c.Git.GetRemote(ctx, workTree, opt.Remote)
	if errors.Is(err, git.ErrRemoteNotFound) {
		return err
	}
	if err != nil {
		return errors.WithMessage(err, "failed to get remote")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &changeservice.RefreshChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to refresh changes")
	}
	// list current changes if necessary
	var changes []*changemodel.Change
	if opt.CommitID == nil && opt.ID == nil {
		items, err := c.ListChangesService.Do(ctx, &changeservice.ListChangesOpt{
			RepoPath: workTree,
		})
		if err != nil {
			return errors.WithMessage(err, "failed to list changes")
		}
		changes = items.Changes()
	}
	// call sync changes service to get rebase actions
	rebase, err := c.SyncChangesService.Do(ctx, &service.SyncChangesOpt{
		RepoPath:       workTree,
		Remote:         opt.Remote,
		Changes:        changes,
		CommitID:       opt.CommitID,
		ID:             opt.ID,
		Restack:        opt.Restack,
		ProgressWriter: opt.ProgressWriter,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to sync changes")
	}
	// calculate revision to checkout after rebase
	branch, err := c.Git.GetCurrentBranch(ctx, workTree)
	switch {
	case errors.Is(err, git.ErrBranchNotFound):
	// do nothing
	case err != nil:
		return errors.WithMessage(err, "failed to get current branch")
	default:
		rebase.BranchNameBeforeStart = branch
		rebase.Checkouts = append(rebase.Checkouts, changemodel.Checkout{
			Revision: &branch,
		})
		change, err := c.ChangeDAO.GetChangeByLocalBranch(ctx, workTree, branch)
		switch {
		case errors.Is(err, changedal.ErrChangeNotFound):
		// do nothing
		case err != nil:
			return errors.WithMessage(err, "get current change failed")
		default:
			changes, err := c.ChangeDAO.ListAllChanges(ctx, workTree)
			if err != nil {
				return errors.WithMessage(err, "failed to list all changes")
			}
			stackBottomChange, ok := changemodel.FindStackBottomChange(changes, change.ID)
			if !ok {
				return errors.WithMessage(err, "failed to find stack bottom change")
			}
			rebase.Checkouts = append(rebase.Checkouts, changemodel.Checkout{
				Revision: stackBottomChange.TargetBranchName,
			})
		}
	}
	// rebase
	if opt.ProgressWriter != nil {
		_, err := fmt.Fprintf(opt.ProgressWriter, "Rebasing changes if necessary...\n")
		if err != nil {
			return errors.WithMessage(err, "failed to write progress")
		}
	}
	err = c.RebaseService.Do(ctx, &changeservice.RebaseOpt{
		RepoPath: workTree,
		Rebase:   rebase,
	})
	if ce := new(changeservice.RebaseConflictError); errors.As(err, &ce) {
		return c.RebaseUsecase.HandleConflict(ctx, &changeusecase.HandleConflictOpt{
			RepoPath:      workTree,
			ConflictError: ce,
		})
	}
	if err != nil {
		return errors.WithMessage(err, "rebase failed")
	}
	// update worktree
	if opt.ProgressWriter != nil {
		_, err := fmt.Fprintf(opt.ProgressWriter, "Updating worktree...\n")
		if err != nil {
			return errors.WithMessage(err, "failed to write progress")
		}
	}
	err = c.RebaseUsecase.UpdateWorktreeAfterRebase(ctx, &changeusecase.UpdateWorktreeAfterRebaseOpt{
		RepoPath: workTree,
		Rebase:   rebase,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to update worktree")
	}
	return nil
}
