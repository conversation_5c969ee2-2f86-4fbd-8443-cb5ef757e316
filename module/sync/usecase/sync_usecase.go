package usecase

import (
	"context"
	"io"

	changemodel "code.byted.org/vecode/staircase/module/change/model"
)

type SubmitChangesOpt struct {
	Remote         string
	Options        []string
	ProgressWriter io.Writer
}

type SubmitChangesUsecase interface {
	Do(ctx context.Context, opt *SubmitChangesOpt) error
}

type SyncChangesOpt struct {
	Remote         string
	Restack        bool
	CommitID       *string
	ID             *changemodel.ChangeID
	ProgressWriter io.Writer
}

type SyncChangesUsecase interface {
	Do(ctx context.Context, opt *SyncChangesOpt) error
}
