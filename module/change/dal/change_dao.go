package dal

import (
	"context"

	"github.com/pkg/errors"

	"code.byted.org/vecode/staircase/module/change/model"
)

var ErrChangeNotFound = errors.New("change not found")

type ChangeDAO interface {
	GetChange(ctx context.Context, repoPath string, id model.ChangeID) (*model.Change, error)
	SaveChange(ctx context.Context, repoPath string, change *model.Change) (*model.Change, error)
	BatchOperateChanges(ctx context.Context, repoPath string, updates []*model.Change, deletes []model.ChangeID) ([]*model.Change, error)
	GetChangeByLocalBranch(ctx context.Context, repoPath, localBranch string) (*model.Change, error)
	ListAllChanges(ctx context.Context, repoPath string) ([]*model.Change, error)
}
