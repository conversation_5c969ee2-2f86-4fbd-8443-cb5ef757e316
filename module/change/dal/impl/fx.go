package impl

import (
	"go.uber.org/fx"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/util/fxutil"
)

var Module = fx.Options(
	fx.Provide(fxutil.StructConstructor(new(ChangeDAO))),
	fx.Provide(fxutil.Bind(new(ChangeDAO), new(dal.ChangeDAO))),
	fx.Provide(fxutil.StructConstructor(new(RebaseDAO))),
	fx.Provide(fxutil.Bind(new(RebaseDAO), new(dal.RebaseDAO))),
)
