package impl

import (
	"context"

	"github.com/samber/lo"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/util/git"
	"code.byted.org/vecode/staircase/util/gitdb"
)

const (
	changeDBRef  = "refs/staircase/changes"
	changeDBPath = "changes.yaml"
)

type changeDB struct {
	Changes []*model.Change `json:"changes"`
}

type ChangeDAO struct {
	Git git.Git
}

func (d *ChangeDAO) GetChange(ctx context.Context, repoPath string, id model.ChangeID) (*model.Change, error) {
	db, _, err := d.getDB(ctx, repoPath)
	if err != nil {
		return nil, err
	}
	change, found := lo.Find(db.Changes, func(change *model.Change) bool {
		return change.ID == id
	})
	if !found {
		return nil, dal.ErrChangeNotFound
	}
	return change, nil
}

func (d *ChangeDAO) ListAllChanges(ctx context.Context, repoPath string) ([]*model.Change, error) {
	db, _, err := d.getDB(ctx, repoPath)
	if err != nil {
		return nil, err
	}
	return db.Changes, nil
}

func (d *ChangeDAO) GetChangeByLocalBranch(ctx context.Context, repoPath, localBranch string) (*model.Change, error) {
	db, _, err := d.getDB(ctx, repoPath)
	if err != nil {
		return nil, err
	}
	change, found := lo.Find(db.Changes, func(change *model.Change) bool {
		return change.LocalBranchName != nil && *change.LocalBranchName == localBranch
	})
	if !found {
		return nil, dal.ErrChangeNotFound
	}
	return change, nil
}

func (d *ChangeDAO) SaveChange(ctx context.Context, repoPath string, change *model.Change) (*model.Change, error) {
	err := d.modifyDB(ctx, repoPath, func(db *changeDB) error {
		_, idx, found := lo.FindIndexOf(db.Changes, func(c *model.Change) bool {
			return c.ID == change.ID
		})
		if found {
			db.Changes[idx] = change
		} else {
			db.Changes = append(db.Changes, change)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return change, nil
}

func (d *ChangeDAO) BatchOperateChanges(ctx context.Context, repoPath string, updates []*model.Change, deletes []model.ChangeID) ([]*model.Change, error) {
	if len(updates) == 0 && len(deletes) == 0 {
		return nil, nil
	}
	err := d.modifyDB(ctx, repoPath, func(db *changeDB) error {
		// create or update changes
		idxByChangeID := make(map[model.ChangeID]int)
		for i, change := range db.Changes {
			idxByChangeID[change.ID] = i
		}
		for _, change := range updates {
			if idx, found := idxByChangeID[change.ID]; found {
				db.Changes[idx] = change
			} else {
				db.Changes = append(db.Changes, change)
				idxByChangeID[change.ID] = len(db.Changes) - 1
			}
		}
		// delete changes
		db.Changes = lo.Filter(db.Changes, func(change *model.Change, _ int) bool {
			return !lo.Contains(deletes, change.ID)
		})
		return nil
	})
	if err != nil {
		return nil, err
	}
	return updates, nil
}

func (d *ChangeDAO) getDB(ctx context.Context, repoPath string) (db *changeDB, commitID *string, err error) {
	gdb := &gitdb.GitDB[changeDB]{
		Git:      d.Git,
		RepoPath: repoPath,
		RefName:  changeDBRef,
		FileName: changeDBPath,
	}
	return gdb.Get(ctx)
}

func (d *ChangeDAO) modifyDB(ctx context.Context, repoPath string, modifier func(db *changeDB) error) error {
	gdb := &gitdb.GitDB[changeDB]{
		Git:      d.Git,
		RepoPath: repoPath,
		RefName:  changeDBRef,
		FileName: changeDBPath,
	}
	return gdb.Modify(ctx, func(db *changeDB) (string, error) {
		err := modifier(db)
		if err != nil {
			return "", err
		}
		return "", nil
	})
}
