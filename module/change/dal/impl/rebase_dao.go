package impl

import (
	"context"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/util/git"
	"code.byted.org/vecode/staircase/util/gitdb"
)

const (
	rebaseDBRef  = "refs/staircase/rebase"
	rebaseDBPath = "rebase.yaml"
)

type rebaseDB struct {
	Rebase *model.Rebase
}

type RebaseDAO struct {
	Git git.Git
}

func (d *RebaseDAO) GetCurrentRebase(ctx context.Context, repoPath string) (*model.Rebase, error) {
	db, _, err := d.getDB(ctx, repoPath)
	if err != nil {
		return nil, err
	}
	if db.Rebase == nil {
		return nil, dal.ErrRebaseNotFound
	}
	return db.Rebase, nil
}

func (d *RebaseDAO) SaveCurrentRebase(ctx context.Context, repoPath string, task *model.Rebase) error {
	return d.modifyDB(ctx, repoPath, func(db *rebaseDB) error {
		db.Rebase = task
		return nil
	})
}

func (d *RebaseDAO) DeleteCurrentRebase(ctx context.Context, repoPath string) error {
	return d.modifyDB(ctx, repoPath, func(db *rebaseDB) error {
		db.Rebase = nil
		return nil
	})
}

func (d *RebaseDAO) getDB(ctx context.Context, repoPath string) (db *rebaseDB, commitID *string, err error) {
	gdb := &gitdb.GitDB[rebaseDB]{
		Git:      d.Git,
		RepoPath: repoPath,
		RefName:  rebaseDBRef,
		FileName: rebaseDBPath,
	}
	return gdb.Get(ctx)
}

func (d *RebaseDAO) modifyDB(ctx context.Context, repoPath string, modifier func(db *rebaseDB) error) error {
	gdb := &gitdb.GitDB[rebaseDB]{
		Git:      d.Git,
		RepoPath: repoPath,
		RefName:  rebaseDBRef,
		FileName: rebaseDBPath,
	}
	return gdb.Modify(ctx, func(db *rebaseDB) (string, error) {
		err := modifier(db)
		if err != nil {
			return "", err
		}
		return "", nil
	})
}
