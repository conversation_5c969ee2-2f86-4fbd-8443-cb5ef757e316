package dal

import (
	"context"
	"errors"

	"code.byted.org/vecode/staircase/module/change/model"
)

var ErrRebaseNotFound = errors.New("rebase not found")

type RebaseDAO interface {
	GetCurrentRebase(ctx context.Context, repoPath string) (*model.Rebase, error)
	SaveCurrentRebase(ctx context.Context, repoPath string, task *model.Rebase) error
	DeleteCurrentRebase(ctx context.Context, repoPath string) error
}
