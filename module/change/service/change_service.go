package service

import (
	"context"

	"code.byted.org/vecode/staircase/module/change/model"
)

type CreateChangeOpt struct {
	RepoPath     string
	CommitID     string
	BaseCommitID string
	// Required if parent change is nil
	TargetBranch *string

	Title             string
	ParentChange      *model.Change
	CreateLocalBranch bool
	LocalBranch       *string
}

type CreateChangeService interface {
	Do(ctx context.Context, opt *CreateChangeOpt) (*model.Change, error)
}

type ListChangesOpt struct {
	RepoPath                    string
	SameStackAs                 *model.Change
	UpStackFrom                 *model.Change
	DownStackFrom               *model.Change
	WithStackBottomTargetBranch bool
}

type ListChangesService interface {
	// Do will return a list of changes or branches in topological order where parents appear before children
	Do(ctx context.Context, opt *ListChangesOpt) (model.ChangeList, error)
}

type RefreshChangesOpt struct {
	RepoPath string
}

type RefreshChangesService interface {
	Do(ctx context.Context, opt *RefreshChangesOpt) error
}

type RebaseConflictError struct {
	Rebase *model.Rebase
}

func (e *RebaseConflictError) Error() string {
	return "conflict"
}

type RebaseOpt struct {
	RepoPath         string
	Rebase           *model.Rebase
	ContinueCommitID *string
}

type RebaseService interface {
	Do(ctx context.Context, opt *RebaseOpt) error
}

type GetWorktreeStatusService interface {
	Do(ctx context.Context, repoPath string) (*model.WorktreeStatus, error)
}
