package impl

import (
	"go.uber.org/fx"

	"code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/util/fxutil"
)

var Module = fx.Options(
	fx.Provide(fxutil.StructConstructor(new(CreateChangeService))),
	fx.Provide(fxutil.Bind(new(CreateChangeService), new(service.CreateChangeService))),
	fx.Provide(fxutil.StructConstructor(new(ListChangesService))),
	fx.Provide(fxutil.Bind(new(ListChangesService), new(service.ListChangesService))),
	fx.Provide(fxutil.StructConstructor(new(RefreshChangesService))),
	fx.Provide(fxutil.Bind(new(RefreshChangesService), new(service.RefreshChangesService))),
	fx.Provide(fxutil.StructConstructor(new(RebaseService))),
	fx.Provide(fxutil.Bind(new(RebaseService), new(service.RebaseService))),
	fx.Provide(fxutil.StructConstructor(new(GetWorktreeStatusService))),
	fx.Provide(fxutil.Bind(new(GetWorktreeStatusService), new(service.GetWorktreeStatusService))),
)
