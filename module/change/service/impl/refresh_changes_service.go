package impl

import (
	"context"

	"github.com/pkg/errors"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/util/git"
	"code.byted.org/vecode/staircase/util/gitdb"
)

type RefreshChangesService struct {
	Git git.Git
	DAO dal.ChangeDAO
}

func (s *RefreshChangesService) Do(ctx context.Context, opt *service.RefreshChangesOpt) error {
	changes, err := s.DAO.ListAllChanges(ctx, opt.RepoPath)
	if err != nil {
		return errors.WithMessage(err, "list sorted changes failed")
	}
	changesByID := make(map[model.ChangeID]*model.Change)
	var updatedChangeIDs []model.ChangeID
	for _, change := range changes {
		if change == nil {
			continue
		}
		changesByID[change.ID] = change
		if change.LocalBranchName == nil {
			continue
		}
		newCommit, err := s.Git.GetCommit(ctx, opt.RepoPath, git.BranchRefPrefix+*change.LocalBranchName)
		if errors.Is(err, git.ErrCommitNotFound) {
			continue
		}
		if err != nil {
			return errors.WithMessage(err, "failed to get new commit")
		}
		if change.LatestVersion().CommitID != newCommit.ID {
			newVersion := &model.ChangeVersion{
				CommitID:     newCommit.ID,
				BaseCommitID: change.LatestVersion().BaseCommitID,
			}
			change.Versions = append(change.Versions, newVersion)
			updatedChangeIDs = append(updatedChangeIDs, change.ID)
		}
	}
	updatedChanges := make([]*model.Change, len(updatedChangeIDs))
	for i, changeID := range updatedChangeIDs {
		updatedChanges[i] = changesByID[changeID]
	}
	_, err = s.DAO.BatchOperateChanges(gitdb.WithAnnotation(ctx, "auto refresh"), opt.RepoPath, updatedChanges, nil)
	if err != nil {
		return errors.WithMessage(err, "batch save changes failed")
	}
	return nil
}
