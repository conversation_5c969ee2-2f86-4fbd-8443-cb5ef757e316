package impl

import (
	"context"
	"time"

	"github.com/pkg/errors"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/util/git"
)

type CreateChangeService struct {
	DAO dal.ChangeDAO
	Git git.Git
}

func (s *CreateChangeService) Do(ctx context.Context, opt *service.CreateChangeOpt) (*model.Change, error) {
	// calc change id
	changeID, err := model.NewRandChangeID()
	if err != nil {
		return nil, errors.WithMessage(err, "new change id failed")
	}
	var targetBranch *string

	// calc target branch
	if opt.ParentChange == nil {
		targetBranch = opt.TargetBranch
	}

	// build version
	version := &model.ChangeVersion{
		CommitID:     opt.CommitID,
		BaseCommitID: opt.BaseCommitID,
	}

	// build change
	change := &model.Change{
		ID:               changeID,
		CreatedAt:        time.Now(),
		TargetBranchName: targetBranch,
		Title:            opt.Title,
		Versions:         []*model.ChangeVersion{version},
	}

	if opt.ParentChange != nil {
		change.ParentID = &opt.ParentChange.ID
	}
	if opt.CreateLocalBranch {
		branchName := change.DefaultLocalBranchName()
		if opt.LocalBranch != nil {
			branchName = *opt.LocalBranch
		}

		// check local branch's uniqueness
		_, err := s.DAO.GetChangeByLocalBranch(ctx, opt.RepoPath, branchName)
		if err == nil {
			return nil, errors.WithMessage(err, "local branch already bound to another change")
		} else if errors.Is(err, dal.ErrChangeNotFound) {
			// do nothing
		} else {
			return nil, errors.WithMessage(err, "failed to get change by local branch")
		}

		err = s.Git.CreateBranch(ctx, opt.RepoPath, branchName, version.CommitID)
		if err != nil {
			return nil, errors.WithMessage(err, "create local branch failed")
		}
		change.LocalBranchName = &branchName
	}

	// save change
	change, err = s.DAO.SaveChange(ctx, opt.RepoPath, change)
	if err != nil {
		return nil, errors.WithMessage(err, "create change failed")
	}

	return change, nil
}
