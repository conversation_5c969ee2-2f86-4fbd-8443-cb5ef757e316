package impl

import (
	"context"
	"time"

	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/util/git"
)

type RebaseService struct {
	Git git.Git
	DAO dal.ChangeDAO
}

func (s *RebaseService) Do(ctx context.Context, opt *service.RebaseOpt) error {
	repoPath := opt.RepoPath
	// list changes
	oldChanges, err := s.DAO.ListAllChanges(ctx, repoPath)
	if err != nil {
		return errors.WithMessage(err, "failed to list all changes")
	}
	changes := make([]*model.Change, len(oldChanges))
	copy(changes, oldChanges)
	rebase := opt.Rebase.Copy()
	// handle continue
	if opt.ContinueCommitID != nil {
		rebase.Continue(*opt.ContinueCommitID)
	}
	// do rebase
	updatedRefs := make(map[string]string)
	for _, action := range rebase.Actions {
		err := s.handleAction(ctx, repoPath, &changes, updatedRefs, action)
		if errors.Is(err, git.ErrConflict) {
			return &service.RebaseConflictError{
				Rebase: rebase,
			}
		}
		if err != nil {
			return errors.WithMessage(err, "failed to handle action")
		}
	}
	// safe remove changes or replace with clone
	g := (*model.ChangeGraph)(&changes)
	for _, id := range rebase.SafeRemove {
		change, ok := lo.Find(changes, func(item *model.Change) bool {
			return item.ID == id
		})
		if !ok {
			return errors.Errorf("change %s not found", id)
		}
		if change.LatestVersion().Empty() {
			// remove change
			_, err := g.Remove(id)
			if err != nil {
				return errors.WithMessage(err, "failed to remove change")
			}
			if change.LocalBranchName != nil {
				updatedRefs[git.BranchRefPrefix+*change.LocalBranchName] = git.BlankID
			}
		} else {
			// replace change with clone
			err := g.ReplaceWithClone(id)
			if err != nil {
				return errors.WithMessage(err, "failed to replace change with clone")
			}
		}
	}
	// remove changes
	for _, id := range rebase.Remove {
		change, ok := lo.Find(changes, func(item *model.Change) bool {
			return item.ID == id
		})
		if !ok {
			return errors.Errorf("change %s not found", id)
		}
		// remove change
		_, err := g.Remove(id)
		if err != nil {
			return errors.WithMessage(err, "failed to remove change")
		}
		if change.LocalBranchName != nil {
			updatedRefs[git.BranchRefPrefix+*change.LocalBranchName] = git.BlankID
		}
	}
	// save && delete changes
	deletes, _ := lo.Difference(
		lo.Map(oldChanges, func(item *model.Change, _ int) model.ChangeID {
			return item.ID
		}),
		lo.Map(changes, func(item *model.Change, _ int) model.ChangeID {
			return item.ID
		}),
	)
	_, err = s.DAO.BatchOperateChanges(ctx, repoPath, changes, deletes)
	if err != nil {
		return errors.WithMessage(err, "failed to save changes")
	}
	// update refs
	for ref, commitID := range updatedRefs {
		ref := git.RefName(ref)
		if commitID == git.BlankID && ref.IsBranch() {
			backupRef := model.BranchBackupRefPrefix + ref.BranchName()
			err := s.Git.UpdateRef(ctx, repoPath, backupRef, ref.String(), nil)
			if err != nil {
				return errors.WithMessage(err, "failed to backup ref")
			}
		}
		err := s.Git.UpdateRef(ctx, repoPath, ref.String(), commitID, nil)
		if err != nil {
			return errors.WithMessage(err, "failed to update ref")
		}
	}
	return nil
}

func (s *RebaseService) handleAction(ctx context.Context, repoPath string, changes *[]*model.Change, updatedRefs map[string]string, action *model.RebaseAction) error {
	// do rebase
	err := s.rebase(ctx, repoPath, *changes, updatedRefs, action)
	if err != nil {
		return errors.WithMessage(err, "failed to rebase")
	}
	// execute after-rebase commands other than ref update
	if opt := action.ChangeCreate; opt != nil {
		_, ok := lo.Find(*changes, func(item *model.Change) bool {
			return item.ID == opt.ID
		})
		if ok {
			return errors.Errorf("change %s exists", opt.ID)
		}
		*changes = append(*changes, &model.Change{
			ID:               opt.ID,
			ParentID:         opt.ParentID,
			CreatedAt:        time.Now(),
			TargetBranchName: opt.TargetBranchName,
			Title:            opt.Title,
			Versions: []*model.ChangeVersion{
				{
					CommitID:     opt.CommitID,
					BaseCommitID: opt.BaseCommitID,
				},
			},
			CustomAttributes: opt.CustomAttributes,
		})
	}

	if opt := action.ChangeUpdate; opt != nil {
		change, ok := lo.Find(*changes, func(item *model.Change) bool {
			return item.ID == opt.ID
		})
		if !ok {
			return errors.Errorf("change %s not found", opt.ID)
		}
		if opt.Title != nil {
			change.Title = *opt.Title
		}
		lo.ForEach(opt.CustomAttributes, func(item model.CustomAttribute, _ int) {
			change.SetCustomAttribute(item)
		})
		if opt.ParentID != nil {
			change.ParentID = opt.ParentID.Value
		}
		if opt.TargetBranchName != nil {
			change.TargetBranchName = opt.TargetBranchName.Value
		}
		preVersion := change.LatestVersion()
		newVersion := pointer.To(*preVersion)
		if opt.CommitID != nil {
			err := s.evaluateCommitID(ctx, repoPath, *changes, updatedRefs, action, opt.CommitID)
			if err != nil {
				return errors.WithMessage(err, "failed to evaluate commit id")
			}
			newVersion.CommitID = *opt.CommitID.Value
		}
		if opt.BaseCommitID != nil {
			err := s.evaluateCommitID(ctx, repoPath, *changes, updatedRefs, action, opt.BaseCommitID)
			if err != nil {
				return errors.WithMessage(err, "failed to evaluate base commit id")
			}
			newVersion.BaseCommitID = *opt.BaseCommitID.Value
		}
		if *preVersion != *newVersion {
			change.Versions = append(change.Versions, newVersion)
		}
	}

	if opt := action.RefUpdate; opt != nil {
		err := s.evaluateCommitID(ctx, repoPath, *changes, updatedRefs, action, &opt.CommitID)
		if err != nil {
			return errors.WithMessage(err, "failed to evaluate commit id to update ref")
		}
		updatedRefs[opt.Name] = *opt.CommitID.Value
	}

	return nil
}

func (s *RebaseService) evaluateCommitID(ctx context.Context, repoPath string, changes []*model.Change, updatedRefs map[string]string, action *model.RebaseAction, value *model.CommitIDValue) error {
	if value.Value != nil {
		return nil
	}
	if changeID := value.LatestCommitIDOfChange; changeID != nil {
		change, ok := lo.Find(changes, func(item *model.Change) bool {
			return item.ID == *changeID
		})
		if !ok {
			return errors.Errorf("change %s not found", *changeID)
		}
		value.Value = &change.LatestVersion().CommitID
		return nil
	}
	if changeID := value.LatestBaseCommitIDOfChange; changeID != nil {
		change, ok := lo.Find(changes, func(item *model.Change) bool {
			return item.ID == *changeID
		})
		if !ok {
			return errors.Errorf("change %s not found", *changeID)
		}
		value.Value = &change.LatestVersion().BaseCommitID
		return nil
	}
	if value.RebaseCommitID {
		if action.Rebase == nil || action.Rebase.ResultCommitID == nil {
			return errors.New("result commit not set")
		}
		value.Value = action.Rebase.ResultCommitID
		return nil
	}
	if value.CommitIDOfRef != nil {
		if commitID, ok := updatedRefs[*value.CommitIDOfRef]; ok {
			value.Value = &commitID
			return nil
		}
		commit, err := s.Git.GetCommit(ctx, repoPath, *value.CommitIDOfRef)
		if err != nil {
			return errors.WithMessagef(err, "failed to get commit %s", *value.CommitIDOfRef)
		}
		value.Value = &commit.ID
		return nil
	}
	return errors.New("invalid value")
}

func (s *RebaseService) rebase(ctx context.Context, repoPath string, changes []*model.Change, updatedRefs map[string]string, action *model.RebaseAction) error {
	rebase := action.Rebase
	if rebase == nil {
		// don't need rebase
		return nil
	}
	if rebase.Conflict != nil {
		if *rebase.Conflict {
			return errors.New("unresolved conflict")
		}
		// already rebased
		return nil
	}
	// evaluate new base commit id
	err := s.evaluateCommitID(ctx, repoPath, changes, updatedRefs, action, &rebase.NewBaseCommitID)
	if err != nil {
		return errors.WithMessage(err, "failed to evaluate new base commit id")
	}
	// evaluate base commit id
	err = s.evaluateCommitID(ctx, repoPath, changes, updatedRefs, action, &rebase.BaseCommitID)
	if err != nil {
		return errors.WithMessage(err, "failed to evaluate base commit id")
	}
	// evaluate end commit id
	err = s.evaluateCommitID(ctx, repoPath, changes, updatedRefs, action, &rebase.EndCommitID)
	if err != nil {
		return errors.WithMessage(err, "failed to evaluate end commit id")
	}
	// execute rebase
	resultCommitID, err := s.Git.Rebase(ctx, repoPath, &git.RebaseOpt{
		NewBase:             *rebase.NewBaseCommitID.Value,
		Base:                *rebase.BaseCommitID.Value,
		End:                 *rebase.EndCommitID.Value,
		Squash:              rebase.Squash,
		SquashCommitMessage: rebase.SquashCommitMessage,
	})
	if errors.Is(err, git.ErrConflict) {
		rebase.Conflict = pointer.To(true)
		return err
	}
	if err != nil {
		return errors.WithMessage(err, "failed to rebase")
	}
	// save rebase commit id
	rebase.Conflict = pointer.To(false)
	rebase.ResultCommitID = &resultCommitID
	return nil
}
