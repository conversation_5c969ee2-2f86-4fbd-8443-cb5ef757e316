package impl

import (
	"context"

	"github.com/pkg/errors"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/util/git"
)

type ListChangesService struct {
	DAO dal.ChangeDAO
	Git git.Git
}

func (s *ListChangesService) Do(ctx context.Context, opt *service.ListChangesOpt) (model.ChangeList, error) {
	// list all changes
	changes, err := s.DAO.ListAllChanges(ctx, opt.RepoPath)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list all changes")
	}
	// list target branches if needed
	branchesByName := make(map[string]*git.Branch)
	if opt.WithStackBottomTargetBranch {
		// get all bottom changes' target branches
		for _, change := range changes {
			if change.ParentID != nil {
				continue
			}
			if change.TargetBranchName == nil {
				// shouldn't happen
				continue
			}
			if branchesByName[*change.TargetBranchName] != nil {
				continue
			}
			branch, err := s.Git.GetBranch(ctx, opt.RepoPath, *change.TargetBranchName)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to get branch")
			}
			branchesByName[*change.TargetBranchName] = branch
		}
	}
	// build change list
	var list model.ChangeList
	for _, change := range changes {
		list = append(list, model.ChangeListItem{Change: change})
	}
	for _, branch := range branchesByName {
		list = append(list, model.ChangeListItem{Branch: branch})
	}
	// sort changes and branches
	list, err = model.SortChangeList(list)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to sort change list")
	}

	// filter items
	list = model.FilterSortedChangeList(list, &model.FilterChangeListOpt{
		SameStackAs:   opt.SameStackAs,
		UpStackFrom:   opt.UpStackFrom,
		DownStackFrom: opt.DownStackFrom,
	})
	return list, nil
}
