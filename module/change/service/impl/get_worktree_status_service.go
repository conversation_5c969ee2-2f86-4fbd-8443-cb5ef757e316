package impl

import (
	"context"

	"github.com/pkg/errors"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/util/git"
)

type GetWorktreeStatusService struct {
	RebaseDAO dal.RebaseDAO
	Git       git.Git
}

func (s *GetWorktreeStatusService) Do(ctx context.Context, repoPath string) (*model.WorktreeStatus, error) {
	status := &model.WorktreeStatus{}
	// get current rebase
	rebase, err := s.RebaseDAO.GetCurrentRebase(ctx, repoPath)
	if err != nil {
		if !errors.Is(err, dal.ErrRebaseNotFound) {
			return nil, errors.WithMessage(err, "failed to get current rebase")
		}
	} else {
		status.CurrentRebase = rebase
	}

	// get git status
	gitStatus, err := s.Git.GetWorktreeStatus(ctx, repoPath)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get git status")
	}
	status.GitWorktreeStatus = gitStatus
	return status, nil
}
