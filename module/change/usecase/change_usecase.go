package usecase

import (
	"context"
	"errors"

	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/module/change/service"
)

type CreateChangeOpt struct {
	Title       string
	LocalBranch string
}

type CreateChangeUsecase interface {
	Do(ctx context.Context, opt *CreateChangeOpt) (*model.Change, error)
}

var (
	ErrConflict        = errors.New("conflict")
	ErrNotOnBranch     = errors.New("not on branch")
	ErrNotChangeBranch = errors.New("not on a change branch")
	ErrNotInRebase     = errors.New("not in rebase")
	ErrInRebase        = errors.New("in rebase")
	ErrRestackNeeded   = errors.New("restack needed")
)

type HandleConflictOpt struct {
	RepoPath      string
	ConflictError *service.RebaseConflictError
}

type RebaseUsecase interface {
	Do(ctx context.Context, opt *RebaseOpt) error
	Continue(ctx context.Context) error
	Abort(ctx context.Context) error
	HandleConflict(ctx context.Context, opt *HandleConflictOpt) error
	UpdateWorktreeAfterRebase(ctx context.Context, opt *UpdateWorktreeAfterRebaseOpt) error
}

type RebaseOpt struct {
	// ChangeID to rebase, if not specified, current change is used
	ChangeID *model.ChangeID
	// UpStack means whether to rebase only the upstack
	UpStack bool
	// Onto is the branch name or change id to rebase onto
	Onto *string
	// Interactive controls whether to fire an editor to rebase
	Interactive bool
}

type UpdateWorktreeAfterRebaseOpt struct {
	RepoPath string
	Rebase   *model.Rebase
}

type SquashUsecase interface {
	Do(ctx context.Context, opt *SquashOpt) error
}

type SquashOpt struct{}

type FoldUsecase interface {
	Do(ctx context.Context, opt *FoldOpt) error
}

type FoldOpt struct{}

type SplitUsecase interface {
	Do(ctx context.Context, opt *SplitOpt) error
}

type SplitOpt struct {
	ByCommits bool
}

type AbsorbUsecase interface {
	Do(ctx context.Context, opt *AbsorbOpt) error
}

type AbsorbOpt struct {
	DryRun       bool
	ApplyChanges bool
}

type UndoUsecase interface {
	Do(ctx context.Context, opt *UndoOpt) error
}

type UndoOpt struct {
	Interactive bool
}

type RedoUsecase interface {
	Do(ctx context.Context, opt *RedoOpt) error
}

type RedoOpt struct{}

type CommitUsecase interface {
	Do(ctx context.Context, opt *CommitOpt) error
}

type CommitOpt struct {
	Edit bool
}
