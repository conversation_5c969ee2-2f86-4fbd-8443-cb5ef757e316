package impl

import (
	"context"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/module/change/usecase"
	"code.byted.org/vecode/staircase/util/git"
)

type FoldUsecase struct {
	Git                   git.Git
	RebaseDAO             dal.RebaseDAO
	ChangeDAO             dal.ChangeDAO
	RefreshChangesService service.RefreshChangesService
	ListChangesService    service.ListChangesService
	RebaseService         service.RebaseService
	RebaseUsecase         usecase.RebaseUsecase
}

func (c *FoldUsecase) Do(ctx context.Context, _ *usecase.FoldOpt) error {
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// check worktree
	clean, err := c.Git.IsWorktreeClean(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "failed to check work tree")
	}
	if !clean {
		return errors.New("work tree not clean")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &service.RefreshChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessage(err, "refresh changes failed")
	}
	// check if there is a rebase in progress
	_, err = c.RebaseDAO.GetCurrentRebase(ctx, workTree)
	if err == nil {
		return usecase.ErrInRebase
	}
	if !errors.Is(err, dal.ErrRebaseNotFound) {
		return errors.WithMessage(err, "get current rebase failed")
	}
	// get current branch
	branch, err := c.Git.GetCurrentBranch(ctx, workTree)
	if errors.Is(err, git.ErrBranchNotFound) {
		return usecase.ErrNotOnBranch
	}
	if err != nil {
		return errors.WithMessage(err, "get current branch failed")
	}
	// get change by current branch
	change, err := c.ChangeDAO.GetChangeByLocalBranch(ctx, workTree, branch)
	if errors.Is(err, dal.ErrChangeNotFound) {
		return usecase.ErrNotChangeBranch
	}
	if err != nil {
		return errors.WithMessage(err, "get current change failed")
	}
	// return if change got no parent change
	if change.ParentID == nil {
		return nil
	}
	// list all changes
	changeList, err := c.ListChangesService.Do(ctx, &service.ListChangesOpt{
		RepoPath:    workTree,
		SameStackAs: change,
	})
	if err != nil {
		return errors.WithMessagef(err, "failed to list changes")
	}
	changes := changeList.Changes()
	// build rebase actions
	parentChange, ok := lo.Find(changes, func(item *model.Change) bool {
		return item.ID == *change.ParentID
	})
	if !ok {
		return errors.New("parent change not found")
	}
	rebaseActions := model.NewRebaseActionsFromFold(parentChange, change)
	// modify change graph
	graph := (*model.ChangeGraph)(&changes)
	updatedChanges, err := graph.Remove(change.ID)
	if err != nil {
		return errors.WithMessage(err, "failed to fold changes")
	}
	// add rest rebase actions
	rebaseActions = append(rebaseActions, model.NewRebaseActionsFromChanges(graph.UpstackChanges(updatedChanges...), false)...)
	// build rebase
	rebase := &model.Rebase{
		BranchNameBeforeStart: branch,
		Checkouts: []model.Checkout{
			{
				ChangeID: &parentChange.ID,
			},
		},
		Actions: rebaseActions,
		Remove:  []model.ChangeID{change.ID},
	}
	// execute rebase
	err = c.RebaseService.Do(ctx, &service.RebaseOpt{
		RepoPath: workTree,
		Rebase:   rebase,
	})
	if ce := new(service.RebaseConflictError); errors.As(err, &ce) {
		return c.RebaseUsecase.HandleConflict(ctx, &usecase.HandleConflictOpt{
			RepoPath:      workTree,
			ConflictError: ce,
		})
	}
	if err != nil {
		return errors.WithMessage(err, "rebase failed")
	}
	// update worktree
	err = c.RebaseUsecase.UpdateWorktreeAfterRebase(ctx, &usecase.UpdateWorktreeAfterRebaseOpt{
		RepoPath: workTree,
		Rebase:   rebase,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to update worktree")
	}
	return nil
}
