package impl

import (
	"context"
	"io"
	"os"
	"os/exec"

	"github.com/pkg/errors"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/module/change/usecase"
	"code.byted.org/vecode/staircase/util/git"
)

type CommitUsecase struct {
	DAO                      dal.ChangeDAO
	Git                      git.Git
	RefreshChangesService    service.RefreshChangesService
	GetWorktreeStatusService service.GetWorktreeStatusService
	RebaseService            service.RebaseService
}

func (c *CommitUsecase) Do(ctx context.Context, opt *usecase.CommitOpt) error {
	// get current working tree
	repoPath, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &service.RefreshChangesOpt{
		RepoPath: repoPath,
	})
	if err != nil {
		return errors.WithMessage(err, "refresh changes failed")
	}
	// get current branch
	branch, err := c.Git.GetCurrentBranch(ctx, repoPath)
	if errors.Is(err, git.ErrBranchNotFound) {
		// not on a branch, run plain git commit
		return c.doPlainGitCommit(ctx, opt)
	}
	if err != nil {
		return errors.WithMessage(err, "get current branch failed")
	}
	// get current change
	change, err := c.DAO.GetChangeByLocalBranch(ctx, repoPath, branch)
	if errors.Is(err, dal.ErrChangeNotFound) {
		// not on a change branch, run plain git commit
		return c.doPlainGitCommit(ctx, opt)
	}
	if err != nil {
		return errors.WithMessage(err, "get current change failed")
	}
	// list commits of current change
	commits, err := c.Git.ListCommits(ctx, &git.ListCommitsOpt{
		RepoPath:    repoPath,
		Revision:    change.LatestVersion().RevisionRange(),
		FirstParent: true,
		Reverse:     true,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to list change commits")
	}
	// get current worktree status
	status, err := c.GetWorktreeStatusService.Do(ctx, repoPath)
	if err != nil {
		return errors.WithMessage(err, "failed to get worktree status")
	}
	// create a new commit or amend current commit
	if len(commits) == 0 {
		if !status.GitWorktreeStatus.HasStagedChanges() {
			// no staged files
			_, _ = io.WriteString(os.Stdin, status.Format())
			return nil
		}
		// create a new commit
		args := []string{"commit", "-m", change.Title}
		if opt.Edit {
			args = append(args, "--edit")
		}
		cmd := exec.CommandContext(ctx, "git", args...)
		cmd.Stdin = os.Stdin
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
		err = cmd.Run()
		if err != nil {
			return errors.WithMessage(err, "failed to run git commit")
		}
	} else {
		// amend existing commit
		args := []string{"commit", "--amend", "-m", change.Title}
		if opt.Edit {
			args = append(args, "--edit")
		} else {
			args = append(args, "--no-edit")
		}
		cmd := exec.CommandContext(ctx, "git", args...)
		cmd.Stdin = os.Stdin
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
		err = cmd.Run()
		if err != nil {
			return errors.WithMessage(err, "failed to run git commit")
		}
	}
	// get current commit
	commitID, err := c.Git.GetHEADCommitID(ctx, repoPath)
	if err != nil {
		return errors.WithMessage(err, "failed to get HEAD commit id")
	}

	// if a new commit is created
	if commitID != change.LatestVersion().CommitID {
		// update current change
		change.Versions = append(change.Versions, &model.ChangeVersion{
			CommitID:     commitID,
			BaseCommitID: change.LatestVersion().BaseCommitID,
		})
		_, err := c.DAO.SaveChange(ctx, repoPath, change)
		if err != nil {
			return errors.WithMessage(err, "failed to save change")
		}
	}
	return nil
}

func (c *CommitUsecase) doPlainGitCommit(ctx context.Context, opt *usecase.CommitOpt) error {
	cmd := exec.CommandContext(ctx, "git", "commit")
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	err := cmd.Run()
	if err != nil {
		return errors.WithMessage(err, "failed to run git commit")
	}
	return nil
}
