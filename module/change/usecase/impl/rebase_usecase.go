package impl

import (
	"context"
	"io"
	"os"

	mapset "github.com/deckarep/golang-set/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/module/change/usecase"
	"code.byted.org/vecode/staircase/util/editor"
	"code.byted.org/vecode/staircase/util/git"
)

type RebaseUsecase struct {
	Git                      git.Git
	DAO                      dal.ChangeDAO
	RebaseDAO                dal.RebaseDAO
	RebaseService            service.RebaseService
	ListChangesService       service.ListChangesService
	RefreshChangesService    service.RefreshChangesService
	GetWorktreeStatusService service.GetWorktreeStatusService
}

func (c *RebaseUsecase) Do(ctx context.Context, opt *usecase.RebaseOpt) error {
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// check worktree
	clean, err := c.Git.IsWorktreeClean(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "failed to check work tree")
	}
	if !clean {
		return errors.New("work tree not clean")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &service.RefreshChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessage(err, "refresh changes failed")
	}
	// check if there is a rebase in progress
	_, err = c.RebaseDAO.GetCurrentRebase(ctx, workTree)
	if err == nil {
		return usecase.ErrInRebase
	}
	if !errors.Is(err, dal.ErrRebaseNotFound) {
		return errors.WithMessage(err, "get current rebase failed")
	}
	// get current branch
	branch, err := c.Git.GetCurrentBranch(ctx, workTree)
	if errors.Is(err, git.ErrBranchNotFound) {
		return usecase.ErrNotOnBranch
	}
	if err != nil {
		return errors.WithMessage(err, "get current branch failed")
	}
	// get change
	var change *model.Change
	if opt.ChangeID != nil {
		// use specified change
		change, err = c.DAO.GetChange(ctx, workTree, *opt.ChangeID)
		if err != nil {
			return errors.WithMessage(err, "get change failed")
		}
	} else {
		// get change by current branch
		change, err = c.DAO.GetChangeByLocalBranch(ctx, workTree, branch)
		if errors.Is(err, dal.ErrChangeNotFound) {
			return usecase.ErrNotChangeBranch
		}
		if err != nil {
			return errors.WithMessage(err, "get current change failed")
		}
	}
	// calculate onto
	var onto string
	if opt.Onto != nil {
		onto = *opt.Onto
	} else if opt.UpStack {
		onto = change.ID.String()
	} else {
		// use stack bottom change's parent branch
		changes, err := c.DAO.ListAllChanges(ctx, workTree)
		if err != nil {
			return errors.WithMessage(err, "failed to list all changes")
		}
		stackBottomChange, ok := model.FindStackBottomChange(changes, change.ID)
		if !ok {
			return errors.WithMessage(err, "failed to find stack bottom change")
		}
		onto = *stackBottomChange.TargetBranchName
	}
	// list changes
	listChangesOpt := &service.ListChangesOpt{
		RepoPath: workTree,
	}
	if opt.UpStack {
		listChangesOpt.UpStackFrom = change
	} else {
		listChangesOpt.SameStackAs = change
	}
	changeListItems, err := c.ListChangesService.Do(ctx, listChangesOpt)
	if err != nil {
		return errors.WithMessage(err, "list changes failed")
	}
	changesToRebase := changeListItems.Changes()
	// filter changes down the stack of change to rebase onto
	if ontoChangeID := model.ChangeID(onto); ontoChangeID.Valid() {
		ontoChange, err := c.DAO.GetChange(ctx, workTree, ontoChangeID)
		if err != nil {
			return errors.WithMessage(err, "failed to get change to rebase onto")
		}
		changeListToFilter, err := c.ListChangesService.Do(ctx, &service.ListChangesOpt{
			RepoPath:      workTree,
			DownStackFrom: ontoChange,
		})
		if err != nil {
			return errors.WithMessage(err, "failed to list changes to filter")
		}
		changeIDsToFilter := mapset.NewSet(lo.Map(changeListToFilter.Changes(), func(item *model.Change, _ int) model.ChangeID {
			return item.ID
		})...)
		changesToRebase = lo.Filter(changesToRebase, func(item *model.Change, _ int) bool {
			return !changeIDsToFilter.Contains(item.ID)
		})
	}
	// build rebase plan
	var rebaseCommands []*model.RebaseCommand
	for i, change := range changesToRebase {
		if i > 0 {
			// handle fork point
			if change.ParentID != nil && *change.ParentID != changesToRebase[i-1].ID {
				rebaseCommands = append(rebaseCommands, &model.RebaseCommand{
					Name: model.RebaseCommandNameBase,
					Args: []string{string(*change.ParentID)},
				})
			} else if change.ParentID == nil {
				rebaseCommands = append(rebaseCommands, &model.RebaseCommand{
					Name: model.RebaseCommandNameBase,
					Args: []string{*change.TargetBranchName},
				})
			}
		}
		rebaseCommands = append(rebaseCommands, &model.RebaseCommand{
			Name: model.RebaseCommandNamePick,
			Args: []string{string(change.ID)},
		})
	}
	if opt.Interactive {
		// launch an editor to let user modify rebase plan
		// format rebase plan
		rawPlan := model.FormatRebasePlan(rebaseCommands, changeListItems.Changes())
		// create a temp file
		rawPlan, err = editor.EditContentWithTempFile(RebasePlanFileNameTemplate, rawPlan)
		if err != nil {
			return errors.WithMessage(err, "failed to edit rebase plan")
		}
		// parse plan
		rebaseCommands, err = model.ParseRebasePlan(rawPlan)
		if err != nil {
			return errors.WithMessage(err, "failed to parse rebase plan")
		}
	}
	// list all changes
	changes, err := c.DAO.ListAllChanges(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "failed to list all changes")
	}
	// build rebase
	rebase := &model.Rebase{
		BranchNameBeforeStart: branch,
		Checkouts: []model.Checkout{
			{
				Revision: &branch,
			},
		},
		Actions: model.NewRebaseActionsFromRebasePlan(changes, onto, rebaseCommands),
	}

	// do rebase
	err = c.RebaseService.Do(ctx, &service.RebaseOpt{
		RepoPath: workTree,
		Rebase:   rebase,
	})
	if ce := new(service.RebaseConflictError); errors.As(err, &ce) {
		return c.HandleConflict(ctx, &usecase.HandleConflictOpt{
			RepoPath:      workTree,
			ConflictError: ce,
		})
	}
	if err != nil {
		return errors.WithMessage(err, "rebase failed")
	}
	// update work tree
	err = c.UpdateWorktreeAfterRebase(ctx, &usecase.UpdateWorktreeAfterRebaseOpt{
		RepoPath: workTree,
		Rebase:   rebase,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to update worktree")
	}
	return nil
}

const RebasePlanFileNameTemplate = "*.st-rebase-plan"

func (c *RebaseUsecase) Continue(ctx context.Context) error {
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// get current rebase
	rebase, err := c.RebaseDAO.GetCurrentRebase(ctx, workTree)
	if errors.Is(err, dal.ErrRebaseNotFound) {
		return usecase.ErrNotInRebase
	}
	if err != nil {
		return errors.WithMessage(err, "get current rebase failed")
	}
	// get worktree status
	status, err := c.GetWorktreeStatusService.Do(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "failed to get worktree status")
	}
	if status.GitWorktreeStatus.RebaseInProgress {
		// call manual git rebase continue
		err = c.Git.ManualRebaseContinue(ctx, workTree, &git.ManualRebaseContinueOpt{
			Stdin:  os.Stdin,
			Stdout: os.Stdout,
			Stderr: os.Stderr,
		})
		if errors.Is(err, git.ErrConflict) {
			return usecase.ErrConflict
		}
		if err != nil {
			return errors.WithMessage(err, "manual rebase continue failed")
		}
	}
	// get head commit
	commitID, err := c.Git.GetHEADCommitID(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "get head commit id failed")
	}
	// call rebase.Continue
	err = c.RebaseService.Do(ctx, &service.RebaseOpt{
		RepoPath:         workTree,
		Rebase:           rebase,
		ContinueCommitID: &commitID,
	})
	if ce := new(service.RebaseConflictError); errors.As(err, &ce) {
		return c.HandleConflict(ctx, &usecase.HandleConflictOpt{
			RepoPath:      workTree,
			ConflictError: ce,
		})
	}
	if err != nil {
		return errors.WithMessage(err, "rebase continue failed")
	}
	// update work tree
	err = c.UpdateWorktreeAfterRebase(ctx, &usecase.UpdateWorktreeAfterRebaseOpt{
		RepoPath: workTree,
		Rebase:   rebase,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to update worktree")
	}
	// delete current rebase
	err = c.RebaseDAO.DeleteCurrentRebase(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "delete current rebase failed")
	}
	return nil
}

func (c *RebaseUsecase) Abort(ctx context.Context) error {
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// get current rebase
	rebase, err := c.RebaseDAO.GetCurrentRebase(ctx, workTree)
	if errors.Is(err, dal.ErrRebaseNotFound) {
		return usecase.ErrNotInRebase
	}
	if err != nil {
		return errors.WithMessage(err, "get current rebase failed")
	}
	// get worktree status
	status, err := c.GetWorktreeStatusService.Do(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "failed to get worktree status")
	}
	if status.GitWorktreeStatus.RebaseInProgress {
		// call git rebase --abort
		err = c.Git.ManualRebaseAbort(ctx, workTree)
		if err != nil {
			return errors.WithMessage(err, "manual rebase abort failed")
		}
	}
	// go back to branch before start
	err = c.Git.Checkout(ctx, workTree, rebase.BranchNameBeforeStart, false)
	if err != nil {
		return errors.WithMessage(err, "checkout branch before start failed")
	}
	// delete current rebase
	err = c.RebaseDAO.DeleteCurrentRebase(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "delete current rebase failed")
	}
	return nil
}

func (c *RebaseUsecase) HandleConflict(ctx context.Context, opt *usecase.HandleConflictOpt) error {
	// build rebase
	rebase := opt.ConflictError.Rebase
	// save current rebase
	err := c.RebaseDAO.SaveCurrentRebase(ctx, opt.RepoPath, rebase)
	if err != nil {
		return errors.WithMessage(err, "save current rebase failed")
	}
	// calc conflicting rebase action
	action, ok := rebase.FindConflictAction()
	if !ok {
		return errors.New("no conflict action")
	}
	// start manual rebase
	// TODO: process errors other than conflict, handle stdout/stderr
	_ = c.Git.ManualRebase(ctx, opt.RepoPath, &git.RebaseOpt{
		NewBase:             *action.Rebase.NewBaseCommitID.Value,
		Base:                *action.Rebase.BaseCommitID.Value,
		End:                 *action.Rebase.EndCommitID.Value,
		Squash:              action.Rebase.Squash,
		SquashCommitMessage: action.Rebase.SquashCommitMessage,
	})
	// show current status
	status, err := c.GetWorktreeStatusService.Do(ctx, opt.RepoPath)
	if err != nil {
		return errors.WithMessage(err, "failed to get worktree status")
	}
	_, _ = io.WriteString(os.Stdout, status.Format())
	return usecase.ErrConflict
}

func (c *RebaseUsecase) UpdateWorktreeAfterRebase(ctx context.Context, opt *usecase.UpdateWorktreeAfterRebaseOpt) error {
	repoPath := opt.RepoPath
	rebase := opt.Rebase

	// calc revision to check out
	var revision *string
	for _, checkout := range rebase.Checkouts {
		if checkout.Revision != nil {
			_, err := c.Git.GetCommit(ctx, repoPath, *checkout.Revision)
			if errors.Is(err, git.ErrCommitNotFound) {
				continue
			}
			if err != nil {
				return errors.WithMessage(err, "failed to get commit")
			}
			revision = checkout.Revision
			break
		}
		if checkout.ChangeID != nil {
			change, err := c.DAO.GetChange(ctx, repoPath, *checkout.ChangeID)
			if errors.Is(err, dal.ErrChangeNotFound) {
				continue
			}
			if err != nil {
				return errors.WithMessage(err, "failed to get change")
			}
			if change.LocalBranchName != nil {
				revision = change.LocalBranchName
				break
			}
			// create branch
			branch := change.DefaultLocalBranchName()
			err = c.Git.CreateBranch(ctx, repoPath, branch, change.LatestVersion().CommitID)
			if err != nil {
				return errors.WithMessagef(err, "failed to create branch(%s)", branch)
			}
			// update change
			change.LocalBranchName = &branch
			_, err = c.DAO.SaveChange(ctx, repoPath, change)
			if err != nil {
				return errors.WithMessage(err, "failed to save change")
			}
			revision = &branch
			break
		}
	}
	if revision == nil {
		// nothing to checkout
		return nil
	}
	// checkout
	err := c.Git.Checkout(ctx, repoPath, *revision, true)
	if err != nil {
		return errors.WithMessage(err, "failed to checkout")
	}
	// reset
	err = c.Git.Reset(ctx, repoPath, &git.ResetOpt{
		Revision:    "HEAD",
		WorkingTree: true,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to reset")
	}
	return nil
}
