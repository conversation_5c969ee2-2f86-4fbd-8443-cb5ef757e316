package impl

import (
	"context"

	"github.com/pkg/errors"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/module/change/usecase"
	"code.byted.org/vecode/staircase/util/git"
)

type SquashUsecase struct {
	Git                   git.Git
	RebaseDAO             dal.RebaseDAO
	RefreshChangesService service.RefreshChangesService
	ListChangesService    service.ListChangesService
	RebaseService         service.RebaseService
	RebaseUsecase         usecase.RebaseUsecase
}

func (c *SquashUsecase) Do(ctx context.Context, _ *usecase.SquashOpt) error {
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// check worktree
	clean, err := c.Git.IsWorktreeClean(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "failed to check work tree")
	}
	if !clean {
		return errors.New("work tree not clean")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &service.RefreshChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessage(err, "refresh changes failed")
	}
	// check if there is a rebase in progress
	_, err = c.RebaseDAO.GetCurrentRebase(ctx, workTree)
	if err == nil {
		return usecase.ErrInRebase
	}
	if !errors.Is(err, dal.ErrRebaseNotFound) {
		return errors.WithMessage(err, "get current rebase failed")
	}
	// get current branch
	branch, err := c.Git.GetCurrentBranch(ctx, workTree)
	if errors.Is(err, git.ErrBranchNotFound) {
		return usecase.ErrNotOnBranch
	}
	if err != nil {
		return errors.WithMessage(err, "get current branch failed")
	}
	// list all changes
	changeList, err := c.ListChangesService.Do(ctx, &service.ListChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessagef(err, "failed to list changes")
	}
	// build rebase
	rebase := &model.Rebase{
		BranchNameBeforeStart: branch,
		Checkouts: []model.Checkout{
			{
				Revision: &branch,
			},
		},
		Actions: model.NewRebaseActionsFromChanges(changeList.Changes(), true),
	}
	// execute rebase
	err = c.RebaseService.Do(ctx, &service.RebaseOpt{
		RepoPath: workTree,
		Rebase:   rebase,
	})
	if ce := new(service.RebaseConflictError); errors.As(err, &ce) {
		return c.RebaseUsecase.HandleConflict(ctx, &usecase.HandleConflictOpt{
			RepoPath:      workTree,
			ConflictError: ce,
		})
	}
	if err != nil {
		return errors.WithMessage(err, "rebase failed")
	}
	// reset work tree and stage area
	err = c.Git.Reset(ctx, workTree, &git.ResetOpt{
		WorkingTree: true,
	})
	if err != nil {
		return errors.WithMessage(err, "reset work tree and stage area failed")
	}
	return nil
}
