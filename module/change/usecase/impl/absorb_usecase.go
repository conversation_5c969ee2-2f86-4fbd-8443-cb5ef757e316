package impl

import (
	"context"
	"io"
	"os"
	"os/exec"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/multierr"

	"code.byted.org/vecode/staircase/cmd/bubbles"
	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/module/change/usecase"
	executil "code.byted.org/vecode/staircase/util/exec"
	"code.byted.org/vecode/staircase/util/git"
	"code.byted.org/vecode/staircase/util/gitabsorb"
)

type AbsorbUsecase struct {
	Git                   git.Git
	GitAbsorb             gitabsorb.Util
	DAO                   dal.ChangeDAO
	ListChangesService    service.ListChangesService
	RefreshChangesService service.RefreshChangesService
	RebaseService         service.RebaseService
}

func (c *AbsorbUsecase) Do(ctx context.Context, opt *usecase.AbsorbOpt) (err error) {
	// get current working tree
	repoPath, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &service.RefreshChangesOpt{
		RepoPath: repoPath,
	})
	if err != nil {
		return errors.WithMessage(err, "refresh changes failed")
	}
	// list staged diff file paths
	stagedDiffPaths, err := c.Git.ListDiffFilePaths(ctx, &git.ListDiffFilePathsOpt{
		RepoPath:     repoPath,
		FromRevision: "HEAD",
		ToIndex:      true,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to list staged diff paths")
	}
	if len(stagedDiffPaths) == 0 {
		return nil
	}
	// get current branch
	branch, err := c.Git.GetCurrentBranch(ctx, repoPath)
	if errors.Is(err, git.ErrBranchNotFound) {
		return usecase.ErrNotOnBranch
	}
	if err != nil {
		return errors.WithMessage(err, "get current branch failed")
	}
	// get current change
	change, err := c.DAO.GetChangeByLocalBranch(ctx, repoPath, branch)
	if errors.Is(err, dal.ErrChangeNotFound) {
		return usecase.ErrNotChangeBranch
	}
	if err != nil {
		return errors.WithMessage(err, "get current change failed")
	}
	// list down-stack changes
	changeList, err := c.ListChangesService.Do(ctx, &service.ListChangesOpt{
		RepoPath:                    repoPath,
		DownStackFrom:               change,
		WithStackBottomTargetBranch: true,
	})
	if err != nil {
		return errors.WithMessage(err, "list down-stack changes failed")
	}
	// changes to consider
	changes := changeList.Changes()
	// root change
	rootChange := changes[0]
	// check whether restack is needed
	restackNeededByChangeID := model.CalcRestackNeeded(changes, changeList.Branches())
	if lo.Contains(lo.Values(restackNeededByChangeID), true) {
		// return an error to user to stash changes && restack first, then stash pop and continue absorb
		return usecase.ErrRestackNeeded
	}
	// save staged content to a tree
	stagedTreeID, err := c.Git.WriteTree(ctx, repoPath)
	if err != nil {
		return errors.WithMessage(err, "failed to write index to a tree")
	}
	// generate a squash commit for each change
	var squashCommitID string
	{
		parentCommitID := rootChange.LatestVersion().BaseCommitID
		for _, change := range changes {
			commitID, err := c.Git.CommitTree(ctx, &git.CommitTreeOpt{
				RepoPath:        repoPath,
				Revision:        change.LatestVersion().CommitID,
				Message:         string(change.ID),
				ParentRevisions: []string{parentCommitID},
			})
			if err != nil {
				return errors.WithMessage(err, "failed to create squash commit")
			}
			parentCommitID = commitID
		}
		squashCommitID = parentCommitID
	}
	var absorbedCommits []*git.Commit
	//	create a working tree of current change's squash commit, sparse checkout index diff related files
	workTree, clean, err := c.Git.CreateWorkTree(ctx, &git.CreateWorkTreeOpt{
		RepoPath:                repoPath,
		Revision:                squashCommitID,
		SparseCheckoutFilePaths: &stagedDiffPaths,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to create work tree")
	}
	defer func() {
		err = multierr.Combine(err, clean())
	}()
	stackBaseCommitID := rootChange.LatestVersion().BaseCommitID
	// apply index diff to working tree's specific index
	err = c.Git.ReadTree(ctx, &git.ReadTreeOpt{
		RepoPath:       workTree,
		Revision:       stagedTreeID,
		Merge:          true,
		UpdateWorkTree: true,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to read tree into index")
	}
	// call git absorb -f --base <stack root change's base commit id>
	err = c.GitAbsorb.Do(ctx, &gitabsorb.Opt{
		RepoPath:     workTree,
		Force:        true,
		BaseRevision: stackBaseCommitID,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to run git-absorb")
	}
	// read head commit id
	headCommitID, err := c.Git.GetHEADCommitID(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "failed to get head commit id after absorb")
	}
	// auto squash commits generated by git-absorb
	autoSquashedCommitID, err := c.Git.Rebase(ctx, workTree, &git.RebaseOpt{
		NewBase:    stackBaseCommitID,
		Base:       stackBaseCommitID,
		End:        headCommitID,
		AutoSquash: true,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to get auto squashed commit id")
	}
	// read commits since stack root change's base commit id
	absorbedCommits, err = c.Git.ListCommits(ctx, &git.ListCommitsOpt{
		RepoPath:    workTree,
		Revision:    stackBaseCommitID + ".." + autoSquashedCommitID,
		FirstParent: true,
		Reverse:     true,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to list squashed commits")
	}
	// correlate with current change's commits
	if len(absorbedCommits) != len(changes) {
		return errors.New("wrong number of absorbed commits")
	}
	// reword absorbed commits using change's title
	resultCommitIDs := make([]string, len(absorbedCommits))
	{
		parentCommitID := absorbedCommits[0].ParentIDs[0]
		for i, commit := range absorbedCommits {
			commitID, err := c.Git.CommitTree(ctx, &git.CommitTreeOpt{
				RepoPath:        repoPath,
				Revision:        commit.TreeID,
				Message:         changes[i].Title,
				ParentRevisions: []string{parentCommitID},
				Author:          commit.Author,
				Committer:       commit.Committer,
			})
			if err != nil {
				return errors.WithMessage(err, "failed to reword commit message")
			}
			resultCommitIDs[i] = commitID
			parentCommitID = commitID
		}
	}
	// calculate new versions
	newVersions := make([]*model.ChangeVersion, len(changes))
	for i, commitID := range resultCommitIDs {
		var baseCommitID string
		if i == 0 {
			baseCommitID = rootChange.LatestVersion().BaseCommitID
		} else {
			baseCommitID = resultCommitIDs[i-1]
		}
		newVersions[i] = &model.ChangeVersion{
			BaseCommitID: baseCommitID,
			CommitID:     commitID,
		}
	}
	// show preview
	err = c.showPreview(ctx, os.Stdout, repoPath, changes, newVersions)
	if err != nil {
		return errors.WithMessage(err, "failed to gen preview")
	}

	if opt.DryRun {
		return nil
	}

	if !opt.ApplyChanges {
		// ask for user confirmation
		m := &bubbles.Confirmation{
			Title: lipgloss.NewStyle().Bold(true).Render("Apply Changes?(y/N)"),
		}
		mi, err := tea.NewProgram(m).Run()
		if err != nil {
			return errors.WithMessage(err, "failed to get user confirmation")
		}
		m = mi.(*bubbles.Confirmation)
		if !m.Confirmed() {
			return nil
		}
	}

	// update change's commits and local branches
	rebase := &model.Rebase{
		BranchNameBeforeStart: branch,
		Actions:               make([]*model.RebaseAction, len(changes)),
	}
	for i, change := range changes {
		commitID := resultCommitIDs[i]
		var refUpdate *model.RefUpdate
		if change.LocalBranchName != nil {
			refUpdate = &model.RefUpdate{
				Name: git.BranchRefPrefix + *change.LocalBranchName,
				CommitID: model.CommitIDValue{
					Value: &commitID,
				},
			}
		}
		newVersion := newVersions[i]
		rebase.Actions[i] = &model.RebaseAction{
			RefUpdate: refUpdate,
			ChangeUpdate: &model.ChangeUpdate{
				ID: change.ID,
				CommitID: &model.CommitIDValue{
					Value: &newVersion.CommitID,
				},
				BaseCommitID: &model.CommitIDValue{
					Value: &newVersion.BaseCommitID,
				},
			},
		}
	}
	err = c.RebaseService.Do(ctx, &service.RebaseOpt{
		RepoPath: repoPath,
		Rebase:   rebase,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to rebase")
	}
	return nil
}

func (c *AbsorbUsecase) showPreview(ctx context.Context, out io.Writer, repoPath string, changes []*model.Change, newVersions []*model.ChangeVersion) error {
	for i, change := range changes {
		// show title
		_, _ = io.WriteString(out, lipgloss.NewStyle().Bold(true).Foreground(lipgloss.ANSIColor(3)).Render("Showing changes for "))
		_, _ = io.WriteString(out, lipgloss.NewStyle().Bold(true).Underline(true).Foreground(lipgloss.ANSIColor(3)).Render(change.Title))
		_, _ = io.WriteString(out, "\n")

		newVersion := newVersions[i]

		// show rage diff
		fromRevision, toRevision, err := c.Git.PrepareTreesForRangeDiff(ctx, &git.PrepareTreesForRangeDiffOpt{
			RepoPath:        repoPath,
			OldFromRevision: change.LatestVersion().BaseCommitID,
			OldToRevision:   change.LatestVersion().CommitID,
			NewFromRevision: newVersion.BaseCommitID,
			NewToRevision:   newVersion.CommitID,
		})
		if err != nil {
			return errors.WithMessage(err, "failed to prepare trees for range diff")
		}
		cmd := exec.CommandContext(ctx, "git", "--no-pager", "diff", "--color=always", fromRevision, toRevision)
		cmd.Stdout = out
		if err := executil.Run(cmd); err != nil {
			return errors.WithMessage(executil.WrapError(err), "failed to show range diff")
		}
	}
	return nil
}
