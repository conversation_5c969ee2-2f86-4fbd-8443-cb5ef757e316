package impl

import (
	"context"
	"fmt"
	"os"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/multierr"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/module/change/usecase"
	"code.byted.org/vecode/staircase/util/editor"
	"code.byted.org/vecode/staircase/util/git"
	yamlutil "code.byted.org/vecode/staircase/util/yaml"
)

type SplitUsecase struct {
	Git                   git.Git
	DAO                   dal.ChangeDAO
	RebaseDAO             dal.RebaseDAO
	RebaseService         service.RebaseService
	ListChangesService    service.ListChangesService
	RefreshChangesService service.RefreshChangesService
	RebaseUsecase         usecase.RebaseUsecase
}

func (c *SplitUsecase) Do(ctx context.Context, opt *usecase.SplitOpt) error {
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// check worktree
	clean, err := c.Git.IsWorktreeClean(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "failed to check work tree")
	}
	if !clean {
		return errors.New("work tree not clean")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &service.RefreshChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessage(err, "refresh changes failed")
	}
	// check if there is a rebase in progress
	_, err = c.RebaseDAO.GetCurrentRebase(ctx, workTree)
	if err == nil {
		return usecase.ErrInRebase
	}
	if !errors.Is(err, dal.ErrRebaseNotFound) {
		return errors.WithMessage(err, "get current rebase failed")
	}
	// get current branch
	branch, err := c.Git.GetCurrentBranch(ctx, workTree)
	if errors.Is(err, git.ErrBranchNotFound) {
		return usecase.ErrNotOnBranch
	}
	if err != nil {
		return errors.WithMessage(err, "get current branch failed")
	}
	// get change by current branch
	change, err := c.DAO.GetChangeByLocalBranch(ctx, workTree, branch)
	if errors.Is(err, dal.ErrChangeNotFound) {
		return usecase.ErrNotChangeBranch
	}
	if err != nil {
		return errors.WithMessage(err, "get current change failed")
	}
	var splitInputs []*model.SplitInput
	if opt.ByCommits {
		splitInputs, err = c.getSplitInputsByCommits(ctx, workTree, change)
		if err != nil {
			return errors.WithMessage(err, "failed to get split inputs by commits")
		}
	} else {
		splitInputs, err = c.getSplitInputsByDiff(ctx, workTree, change)
		if err != nil {
			return errors.WithMessage(err, "failed to get split inputs by diff")
		}
	}
	// list changes
	changeList, err := c.ListChangesService.Do(ctx, &service.ListChangesOpt{
		RepoPath:    workTree,
		UpStackFrom: change,
	})
	if err != nil {
		return errors.WithMessage(err, "list changes failed")
	}
	changes := changeList.Changes()
	// split in graph
	graph := (*model.ChangeGraph)(&changes)
	splitChanges, err := graph.Split(change.ID, splitInputs)
	if err != nil {
		return errors.WithMessage(err, "failed to split inputs")
	}
	// build rebase actions
	rebaseActions := model.NewRebaseActionsFromSplit(change, splitChanges)
	rebaseActions = append(rebaseActions, model.NewRebaseActionsFromChanges(changes, false)...)
	// build rebase
	rebase := &model.Rebase{
		BranchNameBeforeStart: branch,
		Checkouts: []model.Checkout{
			{
				Revision: change.LocalBranchName,
			},
		},
		Actions: rebaseActions,
	}
	// execute rebase
	err = c.RebaseService.Do(ctx, &service.RebaseOpt{
		RepoPath: workTree,
		Rebase:   rebase,
	})
	if ce := new(service.RebaseConflictError); errors.As(err, &ce) {
		return c.RebaseUsecase.HandleConflict(ctx, &usecase.HandleConflictOpt{
			RepoPath:      workTree,
			ConflictError: ce,
		})
	}
	if err != nil {
		return errors.WithMessage(err, "rebase failed")
	}
	// reset work tree and stage area
	err = c.Git.Reset(ctx, workTree, &git.ResetOpt{
		WorkingTree: true,
	})
	if err != nil {
		return errors.WithMessage(err, "reset work tree and stage area failed")
	}
	return nil
}

const ChangeFormFileNameTemplate = "change-create-*.yaml"

type ChangeForm struct {
	Title yamlutil.StringLiteral `yaml:"Title"`
}

func (c *SplitUsecase) getSplitInputsByDiff(ctx context.Context, repoPath string, change *model.Change) (splitInputs []*model.SplitInput, err error) {
	// list diff file names between latest base commit and latest commit
	diffFilePaths, err := c.Git.ListDiffFilePaths(ctx, &git.ListDiffFilePathsOpt{
		RepoPath:     repoPath,
		FromRevision: change.LatestVersion().BaseCommitID,
		ToRevision:   change.LatestVersion().CommitID,
		UseMergeBase: true,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list diff file paths")
	}
	// create a new worktree to avoid interfering user's worktree
	workTree, clean, err := c.Git.CreateWorkTree(ctx, &git.CreateWorkTreeOpt{
		RepoPath:                repoPath,
		Revision:                change.LatestVersion().CommitID,
		SparseCheckoutFilePaths: &diffFilePaths,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create worktree")
	}
	defer func() {
		err = multierr.Combine(err, clean())
	}()
	// reset index to latest base commit, while marking removed files as intent-to-add
	err = c.Git.Reset(ctx, workTree, &git.ResetOpt{
		Revision:                      change.LatestVersion().BaseCommitID,
		WorkingTree:                   false,
		MarkRemovedPathsAsIntentToAdd: true,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to reset index to base commit")
	}
	for {
		// list worktree diff file paths
		worktreeDiffFilePaths, err := c.Git.ListDiffFilePaths(ctx, &git.ListDiffFilePathsOpt{
			RepoPath:   workTree,
			FromIndex:  true,
			ToWorkTree: true,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to list worktree diff file paths")
		}
		if len(worktreeDiffFilePaths) == 0 {
			// worktree has no diff
			break
		}
		// run interactive git add
		err = c.Git.ManualAdd(ctx, &git.ManualAddOpt{
			RepoPath: workTree,
			Stdin:    os.Stdin,
			Stdout:   os.Stdout,
			Stderr:   os.Stderr,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to run interactive git add")
		}
		// list index diff file paths
		indexDiffFilePaths, err := c.Git.ListDiffFilePaths(ctx, &git.ListDiffFilePathsOpt{
			RepoPath:     workTree,
			FromRevision: "HEAD",
			ToIndex:      true,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to list index diff file paths")
		}
		if len(indexDiffFilePaths) == 0 {
			// index has no diff
			continue
		}
		// ask user to edit change form
		form := ChangeForm{
			Title: yamlutil.StringLiteral(fmt.Sprintf("part %d: %s", len(splitInputs)+1, change.Title)),
		}
		newForm, err := editor.EditYamlWithTempFile(ChangeFormFileNameTemplate, form)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to edit change form")
		}
		// create a new commit based on index and form
		commit, err := c.Git.CreateCommit(ctx, workTree, &git.CreateCommitOpt{
			Message:   string(newForm.Title),
			WithIndex: true,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to create commit")
		}
		// append split input
		splitInputs = append(splitInputs, &model.SplitInput{
			Version: &model.ChangeVersion{
				CommitID:     commit.ID,
				BaseCommitID: commit.ParentIDs[0],
			},
			Title: string(newForm.Title),
		})
	}
	if err != nil {
		return nil, errors.WithMessage(err, "failed to do work in new work tree")
	}
	return splitInputs, nil
}

const SplitFormFileNameTemplate = "change-split-*.yaml"

type SplitForm struct {
	Splits []SplitFormSplit `yaml:"Splits" lc:"ordered from stack top to stack bottom"`
}

type SplitFormSplit struct {
	Title   yamlutil.SingleQuotedString   `yaml:"Title"`
	Commits []*yamlutil.StringWithComment `yaml:"Commits"`
}

func (c *SplitUsecase) getSplitInputsByCommits(ctx context.Context, repoPath string, change *model.Change) ([]*model.SplitInput, error) {
	// list change's commit
	commits, err := c.Git.ListCommits(ctx, &git.ListCommitsOpt{
		RepoPath:    repoPath,
		Revision:    change.LatestVersion().RevisionRange(),
		FirstParent: true,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to list change commits")
	}
	// build split form
	form := SplitForm{
		Splits: lo.Map(commits, func(item *git.Commit, _ int) SplitFormSplit {
			return SplitFormSplit{
				Title: yamlutil.SingleQuotedString(item.Subject()),
				Commits: []*yamlutil.StringWithComment{
					{
						Value:   item.ID,
						Comment: item.Subject(),
					},
				},
			}
		}),
	}
	// let user edit form
	newForm, err := editor.EditYamlWithTempFile(SplitFormFileNameTemplate, form)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to edit split form")
	}
	// convert form to split inputs
	result := make([]*model.SplitInput, len(newForm.Splits))
	for i, split := range lo.Reverse(newForm.Splits) {
		if len(split.Commits) == 0 {
			return nil, errors.New("commits mustn't be empty")
		}
		var baseCommitID string
		if i == 0 {
			baseCommitID = change.LatestVersion().BaseCommitID
		} else {
			baseCommitID = result[i-1].Version.CommitID
		}
		result[i] = &model.SplitInput{
			Title: string(split.Title),
			Version: &model.ChangeVersion{
				CommitID:     split.Commits[0].Value,
				BaseCommitID: baseCommitID,
			},
		}
	}
	return result, nil
}
