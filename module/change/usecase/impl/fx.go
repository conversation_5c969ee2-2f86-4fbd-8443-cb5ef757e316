package impl

import (
	"go.uber.org/fx"

	"code.byted.org/vecode/staircase/module/change/usecase"
	"code.byted.org/vecode/staircase/util/fxutil"
)

var Module = fx.Options(
	fx.Provide(fxutil.StructConstructor(new(CreateChangeUsecase))),
	fx.Provide(fxutil.Bind(new(CreateChangeUsecase), new(usecase.CreateChangeUsecase))),
	fx.Provide(fxutil.StructConstructor(new(RebaseUsecase))),
	fx.Provide(fxutil.Bind(new(RebaseUsecase), new(usecase.RebaseUsecase))),
	fx.Provide(fxutil.StructConstructor(new(SquashUsecase))),
	fx.Provide(fxutil.Bind(new(SquashUsecase), new(usecase.SquashUsecase))),
	fx.Provide(fxutil.StructConstructor(new(FoldUsecase))),
	fx.Provide(fxutil.Bind(new(FoldUsecase), new(usecase.FoldUsecase))),
	fx.Provide(fxutil.StructConstructor(new(SplitUsecase))),
	fx.Provide(fxutil.Bind(new(SplitUsecase), new(usecase.SplitUsecase))),
	fx.Provide(fxutil.StructConstructor(new(AbsorbUsecase))),
	fx.Provide(fxutil.Bind(new(AbsorbUsecase), new(usecase.AbsorbUsecase))),
	fx.Provide(fxutil.StructConstructor(new(UndoUsecase))),
	fx.Provide(fxutil.Bind(new(UndoUsecase), new(usecase.UndoUsecase))),
	fx.Provide(fxutil.StructConstructor(new(RedoUsecase))),
	fx.Provide(fxutil.Bind(new(RedoUsecase), new(usecase.RedoUsecase))),
	fx.Provide(fxutil.StructConstructor(new(CommitUsecase))),
	fx.Provide(fxutil.Bind(new(CommitUsecase), new(usecase.CommitUsecase))),
)
