package impl

import (
	"context"

	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/module/change/usecase"
	"code.byted.org/vecode/staircase/util/git"
)

type CreateChangeUsecase struct {
	Git                   git.Git
	DAO                   dal.ChangeDAO
	CreateChangeService   service.CreateChangeService
	RefreshChangesService service.RefreshChangesService
}

func (c *CreateChangeUsecase) Do(ctx context.Context, opt *usecase.CreateChangeOpt) (*model.Change, error) {
	// get current work tree
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return nil, errors.WithMessage(err, "not inside a git work tree")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &service.RefreshChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "refresh changes failed")
	}

	// get current branch name
	branch, err := c.Git.GetCurrentBranch(ctx, workTree)
	if errors.Is(err, git.ErrBranchNotFound) {
		return nil, usecase.ErrNotOnBranch
	} else if err != nil {
		return nil, errors.WithMessage(err, "get current branch failed")
	}

	// get current change
	change, err := c.DAO.GetChangeByLocalBranch(ctx, workTree, branch)
	if errors.Is(err, dal.ErrChangeNotFound) {
		change = nil
	} else if err != nil {
		return nil, errors.WithMessage(err, "get parent change failed")
	}

	// get current commit id
	commitID, err := c.Git.GetHEADCommitID(ctx, workTree)
	if err != nil {
		return nil, errors.WithMessage(err, "get head commit id failed")
	}

	// get base commit id
	var (
		baseCommitID                 string
		updateTargetBranchToUpstream bool
	)
	if change == nil {
		_, err := c.Git.GetCommit(ctx, workTree, "@{upstream}")
		if err == nil {
			commitsAhead, err := c.Git.ListCommits(ctx, &git.ListCommitsOpt{
				RepoPath:    workTree,
				Revision:    "@{upstream}..",
				FirstParent: true,
				Reverse:     true,
			})
			if err != nil {
				return nil, errors.WithMessage(err, "failed to list commits ahead")
			}
			if len(commitsAhead) != 0 {
				startCommit := commitsAhead[0]
				if len(startCommit.ParentIDs) == 0 {
					return nil, errors.New("cannot detect base commit: no parent commit found")
				}
				baseCommitID = startCommit.ParentIDs[0]
				updateTargetBranchToUpstream = true
			} else {
				baseCommitID = commitID
			}
		} else if errors.Is(err, git.ErrCommitNotFound) {
			// no upstream branch configured
			baseCommitID = commitID
		} else {
			return nil, errors.WithMessage(err, "failed to get commit of upstream")
		}
	} else {
		baseCommitID = commitID
	}

	// calc target branch
	var targetBranch *string
	if change == nil {
		targetBranch = &branch
	}

	// create change
	newChange, err := c.CreateChangeService.Do(ctx, &service.CreateChangeOpt{
		RepoPath:          workTree,
		CommitID:          commitID,
		BaseCommitID:      baseCommitID,
		TargetBranch:      targetBranch,
		Title:             opt.Title,
		ParentChange:      change,
		CreateLocalBranch: true,
		LocalBranch:       pointer.ToOrNil(opt.LocalBranch),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "create change failed")
	}

	err = c.Git.Checkout(ctx, workTree, *newChange.LocalBranchName, false)
	if err != nil {
		return nil, errors.WithMessage(err, "checkout change branch failed")
	}

	// update target branch to its upstream branch
	if updateTargetBranchToUpstream {
		err := c.Git.UpdateRef(ctx, workTree, git.BranchRefPrefix+branch, branch+"@{upstream}", nil)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to update target branch to its upstream branch")
		}
	}

	return newChange, nil
}
