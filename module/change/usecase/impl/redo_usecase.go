package impl

import (
	"context"
	"os"
	"strings"

	"github.com/pkg/errors"

	"code.byted.org/vecode/staircase/module/change/dal"
	"code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/module/change/usecase"
	"code.byted.org/vecode/staircase/util/git"
)

type RedoUsecase struct {
	Git                   git.Git
	RebaseDAO             dal.RebaseDAO
	RefreshChangesService service.RefreshChangesService
	ChangeDAO             dal.ChangeDAO
}

func (c *RedoUsecase) Do(ctx context.Context, opt *usecase.RedoOpt) error {
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// check worktree
	clean, err := c.Git.IsWorktreeClean(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "failed to check work tree")
	}
	if !clean {
		return errors.New("work tree not clean")
	}
	// check if there is a rebase in progress
	_, err = c.RebaseDAO.GetCurrentRebase(ctx, workTree)
	if err == nil {
		return usecase.ErrInRebase
	}
	if !errors.Is(err, dal.ErrRebaseNotFound) {
		return errors.WithMessage(err, "get current rebase failed")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &service.RefreshChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessage(err, "refresh changes failed")
	}
	// read change db log
	commits, err := c.Git.ListCommits(ctx, &git.ListCommitsOpt{
		RepoPath:    workTree,
		Revision:    "refs/staircase/changes",
		FirstParent: true,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to list commits")
	}
	// find the last undo command to undo
	var undoneCommands int
	var commitToUndo *git.Commit
	for _, commit := range commits {
		if strings.TrimSpace(commit.Message) == "redo" {
			undoneCommands++
		} else if strings.TrimSpace(commit.Message) == "undo" {
			undoneCommands--
		} else {
			break
		}
		if undoneCommands < 0 {
			commitToUndo = commit
			break
		}
	}
	if commitToUndo == nil {
		return errors.New("nothing to redo")
	}
	var treeToReset string
	if len(commitToUndo.ParentIDs) == 0 {
		treeToReset = git.EmptyTreeID
	} else {
		treeToReset = commitToUndo.ParentIDs[0] + "^{tree}"
	}
	latestCommit := commits[0]
	// update change db
	newCommitID, err := c.Git.CommitTree(ctx, &git.CommitTreeOpt{
		RepoPath:        workTree,
		Revision:        treeToReset,
		Message:         strings.Join(os.Args[1:], " "),
		ParentRevisions: []string{latestCommit.ID},
	})
	if err != nil {
		return errors.WithMessage(err, "failed to commit tree")
	}
	err = c.Git.UpdateRef(ctx, workTree, "refs/staircase/changes", newCommitID, &latestCommit.ID)
	if err != nil {
		return errors.WithMessage(err, "failed to update db ref")
	}
	// update local branches with latest status in change db
	changes, err := c.ChangeDAO.ListAllChanges(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "failed to list changes")
	}
	refUpdates := make([]git.RefUpdate, 0, len(changes))
	for _, change := range changes {
		if change.LocalBranchName != nil {
			refUpdates = append(refUpdates, git.RefUpdate{
				RefName:     "refs/heads/" + *change.LocalBranchName,
				NewCommitID: change.LatestVersion().CommitID,
			})
		}
	}
	err = c.Git.UpdateRefs(ctx, &git.UpdateRefsOpt{
		RepoPath:   workTree,
		RefUpdates: refUpdates,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to update changes' local branches")
	}
	// reset working tree
	err = c.Git.Reset(ctx, workTree, &git.ResetOpt{
		WorkingTree: true,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to reset")
	}
	return nil
}
