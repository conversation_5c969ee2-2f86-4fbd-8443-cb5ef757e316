package impl

import (
	"testing"

	"github.com/stretchr/testify/require"
	"gopkg.in/yaml.v3"

	yamlutil "code.byted.org/vecode/staircase/util/yaml"
)

func TestSplitForm(t *testing.T) {
	// test marshal
	form := SplitForm{
		Splits: []SplitFormSplit{
			{
				Title: "title",
				Commits: []*yamlutil.StringWithComment{
					{
						Value:   "commit 2",
						Comment: "message 2",
					},
					{
						Value:   "commit 1",
						Comment: "message 1",
					},
				},
			},
		},
	}
	raw, err := yaml.Marshal(form)
	require.NoError(t, err)
	require.Equal(t, `Splits:
    - Title: 'title'
      Commits:
        - commit 2 # message 2
        - commit 1 # message 1
`, string(raw))

	// test unmarshal
	form = SplitForm{}
	err = yaml.Unmarshal([]byte(`
Splits:
  - Title: 'title'
    Commits:
      - commit 2 # message 2
      - commit 1 # message 1`), &form)
	require.NoError(t, err)
	require.Equal(t, SplitForm{
		Splits: []SplitFormSplit{
			{
				Title: "title",
				Commits: []*yamlutil.StringWithComment{
					{
						Value: "commit 2",
					},
					{
						Value: "commit 1",
					},
				},
			},
		},
	}, form)
}
