package model

import (
	"strings"

	"github.com/charmbracelet/lipgloss"
	"github.com/samber/lo"

	"code.byted.org/vecode/staircase/util/git"
)

type WorktreeStatus struct {
	CurrentRebase     *Rebase
	GitWorktreeStatus *git.WorktreeStatus
}

func (s *WorktreeStatus) IsClean() bool {
	return s.CurrentRebase == nil && s.GitWorktreeStatus.IsClean()
}

func (s *WorktreeStatus) Format() string {
	builder := &strings.Builder{}
	if s.GitWorktreeStatus.CurrentBranchName != nil {
		builder.WriteString("On branch ")
		builder.WriteString(*s.GitWorktreeStatus.CurrentBranchName)
		builder.WriteString("\n")
	} else if s.GitWorktreeStatus.CurrentCommitID != nil {
		builder.WriteString(lipgloss.NewStyle().Foreground(lipgloss.ANSIColor(1)).Render("HEAD detached at "))
		builder.WriteString((*s.GitWorktreeStatus.CurrentCommitID)[:7])
		builder.WriteString("\n")
	}
	// show whether worktree is clean
	if s.<PERSON>() {
		builder.WriteString("Worktree is clean.\n")
		return builder.String()
	}
	// show current rebase
	{
		if s.CurrentRebase != nil {
			builder.WriteString("You are in the middle of a rebase.\n")
			builder.WriteString("  (fix conflicts and then run \"st continue\")\n")
			builder.WriteString("  (use \"st abort\" to check out the original branch)\n")
			builder.WriteString("\n")
		}
	}
	// show changes to be committed
	{
		entries := lo.Filter(s.GitWorktreeStatus.TrackedEntries, func(item *git.TrackedEntry, _ int) bool {
			return item.IndexChanged()
		})
		if len(entries) > 0 {
			builder.WriteString("Changes to be committed:\n")
			builder.WriteString(" (use \"st restore --staged <file>...\" to unstage)\n")
			for _, entry := range entries {
				builder.WriteString("  ")
				builder.WriteString(lipgloss.NewStyle().Foreground(lipgloss.ANSIColor(2)).Render(entry.IndexChangeString()))
				builder.WriteString("\n")
			}
			builder.WriteString("\n")
		}
	}
	// show unmerged paths
	{
		entries := lo.Filter(s.GitWorktreeStatus.TrackedEntries, func(item *git.TrackedEntry, _ int) bool {
			return item.Unmerged
		})
		if len(entries) > 0 {
			builder.WriteString("Unmerged paths:\n")
			builder.WriteString(" (use \"st restore --staged <file>...\" to unstage)\n")
			builder.WriteString(" (use \"st add <file>...\" to mark resolution)\n")
			for _, entry := range entries {
				builder.WriteString("  ")
				builder.WriteString(lipgloss.NewStyle().Foreground(lipgloss.ANSIColor(1)).Render(entry.UnmergedChangeString()))
				builder.WriteString("\n")
			}
			builder.WriteString("\n")
		}
	}
	// show changes not staged for commit
	{
		entries := lo.Filter(s.GitWorktreeStatus.TrackedEntries, func(item *git.TrackedEntry, _ int) bool {
			return item.WorkingTreeChanged()
		})
		if len(entries) > 0 {
			builder.WriteString("Changes not staged for commit:\n")
			builder.WriteString(" (use \"st add <file>...\" to update what will be committed)\n")
			builder.WriteString(" (use \"st restore <file>...\" to discard changes in working directory)\n")
			for _, entry := range entries {
				builder.WriteString("  ")
				builder.WriteString(lipgloss.NewStyle().Foreground(lipgloss.ANSIColor(1)).Render(entry.WorkingTreeChangeString()))
				builder.WriteString("\n")
			}
			builder.WriteString("\n")
		}
	}
	// show untracked files
	{
		if len(s.GitWorktreeStatus.UntrackedPaths) > 0 {
			builder.WriteString("Untracked files:\n")
			builder.WriteString(" (use \"st add <file>...\" to include in what will be committed)\n")
			for _, up := range s.GitWorktreeStatus.UntrackedPaths {
				builder.WriteString("  ")
				builder.WriteString(lipgloss.NewStyle().Foreground(lipgloss.ANSIColor(1)).Render(up))
				builder.WriteString("\n")
			}
			builder.WriteString("\n")
		}
	}
	return builder.String()
}

func (s *WorktreeStatus) Rel(basePath string) *WorktreeStatus {
	s.GitWorktreeStatus = s.GitWorktreeStatus.Rel(basePath)
	return s
}
