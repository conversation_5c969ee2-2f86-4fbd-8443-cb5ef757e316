// Code generated by go-enum DO NOT EDIT.
// Version:
// Revision:
// Build Date:
// Built By:

package model

import (
	"fmt"

	"github.com/pkg/errors"
)

const (
	// RebaseCommandNameBase is a RebaseCommandName of type base.
	RebaseCommandNameBase RebaseCommandName = "base"
	// RebaseCommandNamePick is a RebaseCommandName of type pick.
	RebaseCommandNamePick RebaseCommandName = "pick"
)

var ErrInvalidRebaseCommandName = errors.New("not a valid RebaseCommandName")

// String implements the Stringer interface.
func (x RebaseCommandName) String() string {
	return string(x)
}

// String implements the Stringer interface.
func (x RebaseCommandName) IsValid() bool {
	_, err := ParseRebaseCommandName(string(x))
	return err == nil
}

var _RebaseCommandNameValue = map[string]RebaseCommandName{
	"base": RebaseCommandNameBase,
	"pick": Rebase<PERSON>ommandNamePick,
}

// ParseRebaseCommandName attempts to convert a string to a RebaseCommandName.
func ParseRebaseCommandName(name string) (RebaseCommandName, error) {
	if x, ok := _RebaseCommandNameValue[name]; ok {
		return x, nil
	}
	return RebaseCommandName(""), fmt.Errorf("%s is %w", name, ErrInvalidRebaseCommandName)
}
