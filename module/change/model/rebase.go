package model

//go:generate go run github.com/abice/go-enum -f=$GOFILE

import (
	"bytes"
	"strings"

	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/vecode/staircase/util/deepcopy"
	"code.byted.org/vecode/staircase/util/git"
)

// Rebase is a sequence of rebase actions
type Rebase struct {
	// Branch to checkout after abort
	BranchNameBeforeStart string
	// Checkouts to try after success
	Checkouts []Checkout
	// Actions
	Actions []*RebaseAction
	// SafeRemove specifies changes to be removed after rebase if empty or replaced with clone
	SafeRemove []ChangeID
	// Remove specifies changes to be removed after rebase
	Remove []ChangeID
}

func (s *Rebase) FindConflictAction() (*RebaseAction, bool) {
	return lo.Find(s.Actions, func(item *RebaseAction) bool {
		return item.Rebase != nil && item.Rebase.Conflict != nil && *item.Rebase.Conflict
	})
}

func (s *Rebase) Continue(commitID string) {
	action, ok := s.FindConflictAction()
	if !ok {
		return
	}
	action.Rebase.Conflict = pointer.To(false)
	action.Rebase.ResultCommitID = &commitID
}

func (s *Rebase) Copy() *Rebase {
	return deepcopy.Must(s)
}

type Checkout struct {
	Revision *string
	ChangeID *ChangeID
}

type RebaseAction struct {
	Rebase *GitRebase
	// Actions to execute after rebase
	RefUpdate    *RefUpdate
	ChangeUpdate *ChangeUpdate
	ChangeCreate *ChangeCreate
}

type GitRebase struct {
	// Param
	NewBaseCommitID CommitIDValue
	BaseCommitID    CommitIDValue
	EndCommitID     CommitIDValue
	Squash          bool
	// Must be non-nil if squash is true
	SquashCommitMessage *string
	// Result
	Conflict       *bool
	ResultCommitID *string
}

type RefUpdate struct {
	Name     string
	CommitID CommitIDValue
}

type ChangeUpdate struct {
	ID               ChangeID
	Title            *string
	CustomAttributes []CustomAttribute
	ParentID         *Value[ChangeID]
	TargetBranchName *Value[string]
	CommitID         *CommitIDValue
	BaseCommitID     *CommitIDValue
}

type ChangeCreate struct {
	ID               ChangeID
	TargetBranchName *string
	Title            string
	CustomAttributes []CustomAttribute
	ParentID         *ChangeID
	BaseCommitID     string
	CommitID         string
}

type Value[T any] struct {
	Value *T
}

type CommitIDValue struct {
	CommitIDOfRef              *string
	LatestCommitIDOfChange     *ChangeID
	LatestBaseCommitIDOfChange *ChangeID
	RebaseCommitID             bool
	Value                      *string
}

func NewRebaseActionsFromChanges(changes []*Change, squash bool) []*RebaseAction {
	actions := make([]*RebaseAction, 0, len(changes))
	for _, change := range changes {
		var newBaseCommitIDValue, baseCommitIDValue CommitIDValue
		if change.ParentID != nil {
			newBaseCommitIDValue = CommitIDValue{
				LatestCommitIDOfChange: change.ParentID,
			}
			baseCommitIDValue = CommitIDValue{
				LatestCommitIDOfChange: change.ParentID,
			}
		} else if change.TargetBranchName != nil {
			newBaseCommitIDValue = CommitIDValue{
				CommitIDOfRef: pointer.To(git.BranchRefPrefix + *change.TargetBranchName),
			}
			baseCommitIDValue = CommitIDValue{
				CommitIDOfRef: pointer.To(git.BranchRefPrefix + *change.TargetBranchName),
			}
		} else {
			// shouldn't happen
			continue
		}
		var refUpdate *RefUpdate
		if change.LocalBranchName != nil {
			refUpdate = &RefUpdate{
				Name: git.BranchRefPrefix + *change.LocalBranchName,
				CommitID: CommitIDValue{
					RebaseCommitID: true,
				},
			}
		}
		action := &RebaseAction{
			Rebase: &GitRebase{
				NewBaseCommitID: newBaseCommitIDValue,
				BaseCommitID: CommitIDValue{
					LatestBaseCommitIDOfChange: &change.ID,
				},
				EndCommitID: CommitIDValue{
					LatestCommitIDOfChange: &change.ID,
				},
				Squash:              squash,
				SquashCommitMessage: &change.Title,
			},
			RefUpdate: refUpdate,
			ChangeUpdate: &ChangeUpdate{
				ID: change.ID,
				ParentID: &Value[ChangeID]{
					Value: change.ParentID,
				},
				TargetBranchName: &Value[string]{
					Value: change.TargetBranchName,
				},
				CommitID: &CommitIDValue{
					RebaseCommitID: true,
				},
				BaseCommitID: &baseCommitIDValue,
			},
		}
		actions = append(actions, action)
	}
	return actions
}

// RebaseCommand is more high-level, user-facing commands, may replace RebaseAction in the future
type RebaseCommand struct {
	Name RebaseCommandName // pick,base
	Args []string
}

// RebaseCommandName
// ENUM(base,pick)
type RebaseCommandName string

func FormatRebasePlan(commands []*RebaseCommand, changes []*Change) []byte {
	changesByID := lo.KeyBy(changes, func(item *Change) ChangeID {
		return item.ID
	})
	b := bytes.Buffer{}
	for _, command := range commands {
		b.WriteString(command.Name.String())
		b.WriteString(" ")
		b.WriteString(strings.Join(command.Args, " "))
		switch command.Name {
		case RebaseCommandNamePick, RebaseCommandNameBase:
			change := changesByID[ChangeID(command.Args[0])]
			if change != nil {
				b.WriteString(" ")
				b.WriteString(change.Title)
			}
		default:
		}
		b.WriteString("\n")
	}
	b.WriteString(`
# Changes are listed from least to most recent
#
# You can reorder changes by reordering the lines
#
# Commands:
#
#  p, pick <change> = use change
#  b, base (<change> | <branch>) = checkout change/branch and apply further changes from there
#
# Deleting a change from the list will DISCARD it from the edited stack!
`)
	return b.Bytes()
}

func ParseRebasePlan(raw []byte) ([]*RebaseCommand, error) {
	// TODO: validate command args
	var result []*RebaseCommand
	for _, line := range bytes.Split(raw, []byte("\n")) {
		line = bytes.TrimSpace(line)
		if len(line) == 0 {
			continue
		}
		if line[0] == '#' {
			continue
		}
		args := strings.Fields(string(line))
		rawName := args[0]
		name := RebaseCommandName(rawName)
		if !name.IsValid() {
			return nil, errors.WithMessagef(ErrInvalidRebaseCommandName, "not a valid RebaseCommandName: %s", rawName)
		}
		args = args[1:]
		result = append(result, &RebaseCommand{
			Name: name,
			Args: args,
		})
	}
	return result, nil
}

func NewRebaseActionsFromRebasePlan(changes []*Change, onto string, commands []*RebaseCommand) []*RebaseAction {
	var (
		actions         = make([]*RebaseAction, 0, len(commands))
		newBaseChangeID *ChangeID
		newTargetBranch *string
		changesByID     = lo.KeyBy(changes, func(item *Change) ChangeID {
			return item.ID
		})
	)
	commands = append(
		[]*RebaseCommand{
			{
				Name: RebaseCommandNameBase,
				Args: []string{onto},
			},
		},
		commands...)
	for _, c := range commands {
		switch c.Name {
		case RebaseCommandNamePick:
			var (
				newBaseCommitIDValue CommitIDValue
				baseCommitIDValue    CommitIDValue
				changeID             = ChangeID(c.Args[0])
				change               = changesByID[changeID]
				refUpdate            *RefUpdate
				parentIDValue        *Value[ChangeID]
				targetBranchValue    *Value[string]
			)
			if newBaseChangeID != nil {
				newBaseCommitIDValue = CommitIDValue{
					LatestCommitIDOfChange: pointer.To(*newBaseChangeID),
				}
				baseCommitIDValue = CommitIDValue{
					LatestCommitIDOfChange: pointer.To(*newBaseChangeID),
				}
				targetBranchValue = &Value[string]{Value: nil}
				parentIDValue = &Value[ChangeID]{Value: pointer.To(*newBaseChangeID)}
			} else if newTargetBranch != nil {
				newBaseCommitIDValue = CommitIDValue{
					CommitIDOfRef: pointer.To(git.BranchRefPrefix + *newTargetBranch),
				}
				baseCommitIDValue = CommitIDValue{
					CommitIDOfRef: pointer.To(git.BranchRefPrefix + *newTargetBranch),
				}
				targetBranchValue = &Value[string]{Value: pointer.To(*newTargetBranch)}
				parentIDValue = &Value[ChangeID]{Value: nil}
			} else {
				if change.ParentID != nil {
					newBaseCommitIDValue = CommitIDValue{
						LatestCommitIDOfChange: change.ParentID,
					}
					baseCommitIDValue = CommitIDValue{
						LatestCommitIDOfChange: change.ParentID,
					}
				} else if change.TargetBranchName != nil {
					newBaseCommitIDValue = CommitIDValue{
						CommitIDOfRef: pointer.To(git.BranchRefPrefix + *change.TargetBranchName),
					}
					baseCommitIDValue = CommitIDValue{
						CommitIDOfRef: pointer.To(git.BranchRefPrefix + *change.TargetBranchName),
					}
				} else {
					// shouldn't happen
					continue
				}
			}
			if change.LocalBranchName != nil {
				refUpdate = &RefUpdate{
					Name: git.BranchRefPrefix + *change.LocalBranchName,
					CommitID: CommitIDValue{
						RebaseCommitID: true,
					},
				}
			}
			action := &RebaseAction{
				Rebase: &GitRebase{
					NewBaseCommitID: newBaseCommitIDValue,
					BaseCommitID: CommitIDValue{
						LatestBaseCommitIDOfChange: &changeID,
					},
					EndCommitID: CommitIDValue{
						LatestCommitIDOfChange: &changeID,
					},
				},
				RefUpdate: refUpdate,
				ChangeUpdate: &ChangeUpdate{
					ID:               changeID,
					ParentID:         parentIDValue,
					TargetBranchName: targetBranchValue,
					CommitID: &CommitIDValue{
						RebaseCommitID: true,
					},
					BaseCommitID: &baseCommitIDValue,
				},
			}
			newBaseChangeID = &changeID
			newTargetBranch = nil
			actions = append(actions, action)
		case RebaseCommandNameBase:
			base := c.Args[0]
			changeID := ChangeID(base)
			if changeID.Valid() {
				newBaseChangeID = &changeID
				newTargetBranch = nil
			} else {
				newBaseChangeID = nil
				newTargetBranch = &base
			}
		default:
			continue
		}
	}
	return actions
}

// NewRebaseActionsFromFold calculate rebase actions from fold operation.
func NewRebaseActionsFromFold(parentChange, changeToFold *Change) []*RebaseAction {
	// rebase change to fold on top of parent change and update parent change's commit id
	var refUpdate *RefUpdate
	if parentChange.LocalBranchName != nil {
		refUpdate = &RefUpdate{
			Name: git.BranchRefPrefix + *parentChange.LocalBranchName,
			CommitID: CommitIDValue{
				RebaseCommitID: true,
			},
		}
	}
	action := &RebaseAction{
		Rebase: &GitRebase{
			NewBaseCommitID: CommitIDValue{
				LatestCommitIDOfChange: &parentChange.ID,
			},
			BaseCommitID: CommitIDValue{
				LatestBaseCommitIDOfChange: &changeToFold.ID,
			},
			EndCommitID: CommitIDValue{
				LatestCommitIDOfChange: &changeToFold.ID,
			},
		},
		RefUpdate: refUpdate,
		ChangeUpdate: &ChangeUpdate{
			ID: parentChange.ID,
			CommitID: &CommitIDValue{
				RebaseCommitID: true,
			},
		},
	}
	return []*RebaseAction{action}
}

// NewRebaseActionsFromSplit calculate rebase actions from split operation.
func NewRebaseActionsFromSplit(oldChange *Change, splitChanges []*Change) []*RebaseAction {
	result := make([]*RebaseAction, 0, len(splitChanges))
	for _, newChange := range splitChanges {
		var refUpdate *RefUpdate
		if newChange.LocalBranchName != nil {
			refUpdate = &RefUpdate{
				Name: git.BranchRefPrefix + *newChange.LocalBranchName,
				CommitID: CommitIDValue{
					Value: &newChange.LatestVersion().CommitID,
				},
			}
		}
		if newChange.ID == oldChange.ID {
			result = append(result, &RebaseAction{
				RefUpdate: refUpdate,
				ChangeUpdate: &ChangeUpdate{
					ID:    newChange.ID,
					Title: &newChange.Title,
					ParentID: &Value[ChangeID]{
						Value: newChange.ParentID,
					},
					TargetBranchName: &Value[string]{
						Value: newChange.TargetBranchName,
					},
					CommitID: &CommitIDValue{
						Value: &newChange.LatestVersion().CommitID,
					},
					BaseCommitID: &CommitIDValue{
						Value: &newChange.LatestVersion().BaseCommitID,
					},
				},
			})
		} else {
			result = append(result, &RebaseAction{
				RefUpdate: refUpdate,
				ChangeCreate: &ChangeCreate{
					ID:               newChange.ID,
					TargetBranchName: newChange.TargetBranchName,
					Title:            newChange.Title,
					ParentID:         newChange.ParentID,
					BaseCommitID:     newChange.LatestVersion().BaseCommitID,
					CommitID:         newChange.LatestVersion().CommitID,
				},
			})
		}
	}
	return result
}
