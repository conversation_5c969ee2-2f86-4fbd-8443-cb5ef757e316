package model

import (
	"crypto/rand"
	"encoding/hex"
	"regexp"
	"time"

	"github.com/AlekSi/pointer"
	mapset "github.com/deckarep/golang-set/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/vecode/staircase/util/deepcopy"
	"code.byted.org/vecode/staircase/util/git"
	"code.byted.org/vecode/staircase/util/toposort"
)

// BranchBackupRefPrefix is where branches deleted by staircase would be hosted
const BranchBackupRefPrefix = "refs/staircase/branch-backup/"

var changeIDRegex = regexp.MustCompile(`^I[0-9a-f]{40}$`)

type ChangeID string

func (id ChangeID) Abbrev() string {
	return string(id[0:7])
}

func (id ChangeID) Valid() bool {
	return changeIDRegex.MatchString(string(id))
}

func (id ChangeID) String() string {
	return string(id)
}

func NewRandChangeID() (ChangeID, error) {
	raw := make([]byte, 20)
	if _, err := rand.Read(raw); err != nil {
		return "", errors.WithMessage(err, "read random bytes failed")
	}
	encoded := make([]byte, 40)
	hex.Encode(encoded, raw)
	return ChangeID("I" + string(encoded)), nil
}

type Change struct {
	ID ChangeID
	// Exactly one of ParentID and TargetBranchName must be non-nil
	ParentID *ChangeID
	// Exactly one of ParentID and TargetBranchName must be non-nil
	TargetBranchName *string
	CreatedAt        time.Time
	Title            string
	LocalBranchName  *string
	Versions         []*ChangeVersion
	CustomAttributes []CustomAttribute
}

func (c *Change) Equal(other *Change) bool {
	return c != nil &&
		other != nil &&
		c.Title == other.Title &&
		pointer.Get(c.ParentID) == pointer.Get(other.ParentID) &&
		pointer.Get(c.TargetBranchName) == pointer.Get(other.TargetBranchName) &&
		c.LatestVersion().Equal(other.LatestVersion())
}

func (c *Change) NormalizedTitle() string {
	return regexp.MustCompile(`[^a-zA-Z0-9_\-.]+`).ReplaceAllString(c.Title, "-")
}

func (c *Change) DefaultLocalBranchName() string {
	return c.NormalizedTitle() + "-" + c.ID.Abbrev()
}

func (c *Change) LatestVersion() *ChangeVersion {
	return c.Versions[len(c.Versions)-1]
}

func (c *Change) Copy() *Change {
	return deepcopy.Must(c)
}

func (c *Change) Clone() (*Change, error) {
	var err error
	cc := c.Copy()
	cc.ID, err = NewRandChangeID()
	if err != nil {
		return nil, err
	}
	return cc, nil
}

func (c *Change) SetCustomAttribute(attribute CustomAttribute) {
	c.CustomAttributes = lo.Filter(c.CustomAttributes, func(item CustomAttribute, _ int) bool {
		return item.Name != attribute.Name
	})
	c.CustomAttributes = append(c.CustomAttributes, attribute)
}

type ChangeVersion struct {
	CommitID     string
	BaseCommitID string
}

func (v *ChangeVersion) Empty() bool {
	return v.CommitID == v.BaseCommitID
}

func (v *ChangeVersion) RevisionRange() string {
	return v.BaseCommitID + ".." + v.CommitID
}

func (v *ChangeVersion) Equal(other *ChangeVersion) bool {
	return v.CommitID == other.CommitID && v.BaseCommitID == other.BaseCommitID
}

type CustomAttribute struct {
	Name        string
	DisplayName *string
	Value       string
}

type ChangeList []ChangeListItem

func (l ChangeList) Changes() []*Change {
	return lo.WithoutEmpty(lo.Map(l, func(item ChangeListItem, _ int) *Change {
		return item.Change
	}))
}

func (l ChangeList) Branches() []*git.Branch {
	return lo.WithoutEmpty(lo.Map(l, func(item ChangeListItem, _ int) *git.Branch {
		return item.Branch
	}))
}

func SortChangeList(list ChangeList) (ChangeList, error) {
	sortBranches := lo.SomeBy(list, func(item ChangeListItem) bool {
		return item.Branch != nil
	})
	g := new(toposort.Graph)
	itemsByKey := lo.KeyBy(list, func(item ChangeListItem) string {
		return item.Key()
	})
	for _, item := range list {
		g.AddNode(item)
		if item.ParentKey() == nil {
			continue
		}
		if item.Change != nil && item.Change.ParentID == nil && !sortBranches {
			continue
		}
		parentItem, ok := itemsByKey[*item.ParentKey()]
		if !ok {
			return nil, errors.Errorf("parent item not found: %s", *item.ParentKey())
		}
		g.AddEdge(parentItem, item)
	}
	nodes, err := g.Sort()
	if err != nil {
		return nil, errors.WithMessage(err, "failed to sort changes")
	}
	return lo.Map(nodes, func(node toposort.Node, _ int) ChangeListItem {
		return node.(ChangeListItem)
	}), nil
}

type FilterChangeListOpt struct {
	SameStackAs   *Change
	UpStackFrom   *Change
	DownStackFrom *Change
}

func FilterSortedChangeList(list ChangeList, opt *FilterChangeListOpt) ChangeList {
	itemsByKey := lo.KeyBy(list, func(item ChangeListItem) string {
		return item.Key()
	})
	filteredKeys := lo.Map(list, func(t ChangeListItem, _ int) string {
		return t.Key()
	})
	if opt.SameStackAs != nil {
		var keys []string
		item := ChangeListItem{Change: opt.SameStackAs}
		// filter down-stack items
		for {
			keys = append(keys, item.Key())
			if item.ParentKey() == nil {
				break
			}
			item = itemsByKey[*item.ParentKey()]
		}
		// filter up-stack items
		item = ChangeListItem{Change: opt.SameStackAs}
		parentKeys := map[string]bool{
			item.Key(): true,
		}
		for _, item := range list {
			if parentKeys[pointer.Get(item.ParentKey())] {
				parentKeys[item.Key()] = true
				keys = append(keys, item.Key())
			}
		}
		filteredKeys = lo.Intersect(filteredKeys, keys)
	}
	if opt.UpStackFrom != nil {
		var keys []string
		item := ChangeListItem{Change: opt.UpStackFrom}
		keys = append(keys, item.Key())
		parentKeys := map[string]bool{
			item.Key(): true,
		}
		for _, item := range list {
			if parentKeys[pointer.Get(item.ParentKey())] {
				parentKeys[item.Key()] = true
				keys = append(keys, item.Key())
			}
		}
		filteredKeys = lo.Intersect(filteredKeys, keys)
	}
	if opt.DownStackFrom != nil {
		var keys []string
		item := ChangeListItem{Change: opt.DownStackFrom}
		for {
			keys = append(keys, item.Key())
			if item.ParentKey() == nil {
				break
			}
			item = itemsByKey[*item.ParentKey()]
		}
		filteredKeys = lo.Intersect(filteredKeys, keys)
	}
	return lo.Filter(list, func(item ChangeListItem, _ int) bool {
		return lo.Contains(filteredKeys, item.Key())
	})
}

type ChangeListItem struct {
	Change *Change
	Branch *git.Branch
}

func (t ChangeListItem) Key() string {
	if t.Change != nil {
		return "c" + string(t.Change.ID)
	}
	if t.Branch != nil {
		return "b" + t.Branch.Name
	}
	return "unknown"
}

func (t ChangeListItem) ParentKey() *string {
	if t.Change != nil {
		if t.Change.ParentID != nil {
			return pointer.To("c" + string(*t.Change.ParentID))
		} else if t.Change.TargetBranchName != nil {
			return pointer.To("b" + *t.Change.TargetBranchName)
		}
	}
	return nil
}

func (t ChangeListItem) Less(other toposort.Node) bool {
	o := other.(ChangeListItem)
	if t.CreatedAt() != o.CreatedAt() {
		return t.CreatedAt().Before(o.CreatedAt())
	}
	return t.Name() < o.Name()
}

func (t ChangeListItem) CreatedAt() time.Time {
	if t.Change != nil {
		return t.Change.CreatedAt
	}
	if t.Branch != nil {
		return t.Branch.Commit.Committer.When
	}
	return time.Time{}
}

func (t ChangeListItem) Name() string {
	if t.Change != nil {
		return t.Change.Title
	}
	if t.Branch != nil {
		return t.Branch.Name
	}
	return ""
}

// ChangeGraph handles topological logic
type ChangeGraph []*Change

func (g *ChangeGraph) Update(updates []*Change) error {
	changesByID := lo.KeyBy(*g, func(item *Change) ChangeID {
		return item.ID
	})
	for _, update := range updates {
		change := changesByID[update.ID]
		if change == nil {
			changesByID[update.ID] = update
		} else {
			change.ParentID = update.ParentID
			change.TargetBranchName = update.TargetBranchName
		}
	}
	changes, err := SortChanges(lo.Values(changesByID))
	if err != nil {
		return errors.WithMessage(err, "failed to sort changes")
	}
	*g = changes
	return nil
}

func (g *ChangeGraph) Remove(id ChangeID) ([]*Change, error) {
	changeToRemove, idx, ok := lo.FindIndexOf(*g, func(item *Change) bool {
		return item.ID == id
	})
	if !ok {
		return nil, errors.Errorf("change(%s) not found", id)
	}
	var updatedChanges []*Change
	for _, change := range *g {
		if change.ParentID != nil && *change.ParentID == id {
			updatedChanges = append(updatedChanges, change)
			change.ParentID = changeToRemove.ParentID
			if change.ParentID == nil {
				change.TargetBranchName = changeToRemove.TargetBranchName
			}
		}
	}
	*g = append((*g)[:idx], (*g)[idx+1:]...)
	return updatedChanges, nil
}

func (g *ChangeGraph) ReplaceWithClone(id ChangeID) error {
	changeToReplace, idx, ok := lo.FindIndexOf(*g, func(item *Change) bool {
		return item.ID == id
	})
	if !ok {
		return errors.Errorf("change(%s) not found", id)
	}
	newChange, err := changeToReplace.Clone()
	if err != nil {
		return err
	}
	for _, change := range *g {
		if change.ParentID != nil && *change.ParentID == id {
			change.ParentID = &newChange.ID
		}
	}
	(*g)[idx] = newChange
	return nil
}

func (g *ChangeGraph) Split(id ChangeID, inputs []*SplitInput) (splitChanges []*Change, err error) {
	changeToReplace, idx, ok := lo.FindIndexOf(*g, func(item *Change) bool {
		return item.ID == id
	})
	if !ok {
		return nil, errors.Errorf("change(%s) not found", id)
	}
	if len(inputs) == 0 {
		return nil, errors.New("empty inputs")
	}
	changeToReplace.Versions = append(changeToReplace.Versions, inputs[0].Version)
	changeToReplace.Title = inputs[0].Title
	prevChange := changeToReplace
	newChanges := make([]*Change, len(inputs)-1)
	now := time.Now()
	for i := 1; i < len(inputs); i++ {
		id, err := NewRandChangeID()
		if err != nil {
			return nil, errors.WithMessage(err, "failed to gen change id")
		}
		change := &Change{
			ID:        id,
			ParentID:  pointer.To(prevChange.ID),
			CreatedAt: now,
			Title:     inputs[i].Title,
			Versions: []*ChangeVersion{
				inputs[i].Version,
			},
		}
		newChanges[i-1] = change
		prevChange = change
	}
	for _, change := range *g {
		if change.ParentID != nil && *change.ParentID == id {
			change.ParentID = &newChanges[len(newChanges)-1].ID
		}
	}
	*g = append((*g)[:idx+1], append(newChanges, (*g)[idx+1:]...)...)
	return append([]*Change{changeToReplace}, newChanges...), nil
}

type SplitInput struct {
	Version *ChangeVersion
	Title   string
}

func (g *ChangeGraph) UpstackChanges(changes ...*Change) []*Change {
	parentIDs := mapset.NewSet(lo.Map(changes, func(item *Change, _ int) ChangeID {
		return item.ID
	})...)
	result := changes
	for _, change := range *g {
		if parentIDs.Contains(change.ID) {
			continue
		}
		if change.ParentID == nil {
			continue
		}
		if !parentIDs.Contains(*change.ParentID) {
			continue
		}
		result = append(result, change)
		parentIDs.Add(change.ID)
	}
	return result
}

func GroupSortedChangesByTargetBranch(changes []*Change) map[string][]*Change {
	result := make(map[string][]*Change, len(changes))
	branchesByChangeID := make(map[ChangeID]string, len(changes))
	for _, change := range changes {
		var branch string
		if change.ParentID != nil {
			branch = branchesByChangeID[*change.ParentID]
		} else if change.TargetBranchName != nil {
			branch = *change.TargetBranchName
		}
		result[branch] = append(result[branch], change)
		branchesByChangeID[change.ID] = branch
	}
	return result
}

// CalcRestackNeeded calculates whether a change needs to be restacked.
// Parent changes must appear before child changes
func CalcRestackNeeded(changes []*Change, branches []*git.Branch) map[ChangeID]bool {
	branchesByName := lo.KeyBy(branches, func(item *git.Branch) string {
		return item.Name
	})
	changesByID := lo.KeyBy(changes, func(item *Change) ChangeID {
		return item.ID
	})
	result := make(map[ChangeID]bool, len(changes))
	for _, change := range changes {
		if change.TargetBranchName != nil {
			branch := branchesByName[*change.TargetBranchName]
			if branch == nil {
				continue
			}
			result[change.ID] = change.LatestVersion().BaseCommitID != branch.Commit.ID
			continue
		}
		if change.ParentID != nil {
			parentChange := changesByID[*change.ParentID]
			if parentChange == nil {
				continue
			}
			result[change.ID] = result[parentChange.ID] || change.LatestVersion().BaseCommitID != parentChange.LatestVersion().CommitID
		}
	}
	return result
}

func FindStackBottomChange(changes []*Change, id ChangeID) (*Change, bool) {
	changesByID := lo.KeyBy(changes, func(item *Change) ChangeID {
		return item.ID
	})
	iter := 0
	parentID := id
	for {
		iter++
		if iter > len(changes) {
			return nil, false
		}
		change := changesByID[parentID]
		if change == nil {
			return nil, false
		}
		if change.ParentID == nil {
			return change, true
		}
		parentID = *change.ParentID
	}
}

func SortChanges(changes []*Change) ([]*Change, error) {
	list, err := SortChangeList(lo.Map(changes, func(item *Change, _ int) ChangeListItem {
		return ChangeListItem{Change: item}
	}))
	if err != nil {
		return nil, errors.WithMessage(err, "failed to sort change list")
	}
	return list.Changes(), nil
}
