package main

import (
	"context"
	"os"

	"github.com/spf13/cobra"
	"go.uber.org/fx"

	"code.byted.org/vecode/staircase/cmd"
	"code.byted.org/vecode/staircase/module/change"
	"code.byted.org/vecode/staircase/module/sync"
	"code.byted.org/vecode/staircase/plugin/byted"
	gitimpl "code.byted.org/vecode/staircase/util/git/impl"
	gitabsorbimpl "code.byted.org/vecode/staircase/util/gitabsorb/impl"
)

func main() {
	app := fx.New(
		fx.NopLogger,
		cmd.Module,
		change.Module,
		sync.Module,
		gitimpl.Module,
		gitabsorbimpl.Module,
		byted.Module,
	)
	err := app.Start(context.Background())
	if err != nil {
		cobra.CheckErr(err.Error())
	}
	sig := <-app.Wait()
	os.Exit(sig.ExitCode)
}
