#!/bin/sh

commit_msg_content=$(
cat << "EOF"
#!/bin/sh
# From Gerrit Code Review 3.1.3
#
# Part of Gerrit Code Review (https://www.gerritcodereview.com/)
#
# Copyright (C) 2009 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# If the previous commit-msg hook exist, execute previous hook first
repo_root=$(git rev-parse --show-toplevel)
old_commit_msg_path="${repo_root}/.git/hooks/commit-msg.old"
if test -e "${old_commit_msg_path}"; then
    if ! "${old_commit_msg_path}" ; then
        echo "${old_commit_msg_path} returned a non-zero exit code."
        exit 1
    fi
fi

# avoid [[ which is not POSIX sh.
if test "$#" != 1 ; then
  echo "$0 requires an argument."
  exit 1
fi

if test ! -f "$1" ; then
  echo "file does not exist: $1"
  exit 1
fi

# Do not create a change id if requested
if test "false" = "`git config --bool --get gerrit.createChangeId`" ; then
  exit 0
fi

# $RANDOM will be undefined if not using bash, so do not use set -u
random=$( (whoami ; hostname ; date; cat $1 ; echo $RANDOM) | git hash-object --stdin)
dest="$1.tmp.${random}"

trap "rm -f \"${dest}\"" EXIT

if ! git stripspace --strip-comments < "$1" > "${dest}" ; then
   echo "cannot strip comments from $1"
   exit 1
fi

if test ! -s "${dest}" ; then
  echo "file is empty: $1"
  exit 1
fi

# Avoid the --in-place option which only appeared in Git 2.8
# Avoid the --if-exists option which only appeared in Git 2.15
if ! git -c trailer.ifexists=doNothing interpret-trailers --trailer "Change-Id: I${random}" < "$1" > "${dest}" ; then
  echo "cannot insert change-id line in $1"
  exit 1
fi

if ! mv "${dest}" "$1" ; then
  echo "cannot mv ${dest} to $1"
  exit 1
fi

EOF
)

git_stack_content=$(
cat << EOF
usage() {
  echo "usage: git stack <command>."
  echo ""
  echo "commands"
  echo "--------"
  echo ""
  echo "git stack install:"
  echo "\\\tSetup commit-msg hook for current git repository."
}

install() {
  # If the commit-msg hook exist, rename to commit-msg.old
    commit_msg_path="\$(pwd)/.git/hooks/commit-msg"
    if test -e "\${commit_msg_path}"; then
        echo "Detect existing commit-msg hook. Rename it to commit-msg.old and the new commit-msg hook will execute commit-msg.old first before executing the original script."
        mv "\${commit_msg_path}" "\$(pwd)/.git/hooks/commit-msg.old"
    fi

    # Setup commit-msg
    echo '$commit_msg_content' > "\$commit_msg_path"
    chmod +x "\$commit_msg_path"
    echo "Commit-msg hook has been setup."
}

# Check if current directory a git repository
if ! git rev-parse --is-inside-work-tree >/dev/null 2>&1; then
    echo "Not inside a git repository. Run git stack inside a git repository to setup commit-msg hook."
    exit 1
fi

if test "\$#" -eq 0; then
    usage
    exit 1
fi

if [ "\$1" = "install" ]; then
    install
    exit 0
fi

echo "invalid commands."
usage
exit 1

EOF
)

check_git_alias_and_configure() {
    alias_name="$1"
    alias_value=$(git config --get-regexp "^alias\.$alias_name$")
    if test -n "$alias_value"; then
        read -p "Git alias $1 already exists. Do you want to overwrite it? (y/n): " choice
        if test "$choice" == "${choice#[Yy]}"; then
            echo "Skip git alias $1 configuration."
            return
        fi
    fi

    setting="$2"
    git config --global alias."$alias_name" "$setting"
    echo "Git alias $alias_name configured."
}

# Check if git is installed
if ! command -v git >/dev/null 2>&1; then
    echo "git is not installed. Please install git first."
    exit
fi

# Check if templatedir is configured
template_dir=$(git config --type=path --get init.templatedir)
if test -z "$template_dir"; then
    # If templatedir is not configured, set it to $HOME/.gittemplates
    git config --global init.templatedir $HOME/.gittemplates
    template_dir=$(git config --get init.templatedir)
    echo "Git templatedir is not configured. Setting it to $HOME/.gittemplates."
fi

# Rename existing commit-msg to commit-msg.old
commit_msg_path="$template_dir/hooks/commit-msg"
if test -f "$commit_msg_path"; then
    echo "Detect existing commit-msg hook. Rename it to commit-msg.old and the new commit-msg hook will execute commit-msg.old first before executing the original script."
    mv "$template_dir/hooks/commit-msg" "$template_dir/hooks/commit-msg.old"
fi

# Create new commit-msg hook template
mkdir -p "$template_dir/hooks"
echo "$commit_msg_content" > "$commit_msg_path"
echo "Commit-msg hook created in $commit_msg_path."

# Setup git alias
check_git_alias_and_configure "new" '!f() { git fetch && git checkout -b sandbox/$(git config user.email | awk -F@ "{print \$1}")/$(date +%m-%d)/${2:-$(date +%H-%M)} origin/${1:-master}; }; f'
check_git_alias_and_configure "rv" '!f() { git push origin HEAD:refs/for/${1:-master}; }; f'
check_git_alias_and_configure "cin" 'commit --amend --no-edit'
check_git_alias_and_configure "delbranch" '!f() { git branch | grep sandbox | xargs git branch -D; }; f'

# Create git stack command
echo "\nIn order to create a git stack command in /usr/local/bin to let you to set up stack diff for existing repository,\nthis script need to be run as root, please help to enter password."
echo "$git_stack_content" > "/tmp/git-stack.tmp"
sudo -s <<EOF
  git_stack_path="/usr/local/bin/git-stack"
  mv "/tmp/git-stack.tmp" "\$git_stack_path"
  chmod +x "\$git_stack_path"
  chown root "\$git_stack_path"
EOF

echo "Git stack command installed in /usr/local/bin/git-stack. You can run git stack command to set up stack diff for existing repository."

echo "Done."
