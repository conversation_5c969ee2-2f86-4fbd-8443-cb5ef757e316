package main

import (
	"bytes"
	"flag"
	"log"
	"os"
	"os/exec"
	"strings"

	"github.com/AlekSi/pointer"
	"github.com/Masterminds/semver/v3"
	"github.com/samber/lo"
)

var releaseType = flag.String("release-type", "", "")

func main() {
	flag.Parse()
	// check working tree
	output, err := exec.Command("git", "status", "--porcelain").Output()
	if err != nil {
		log.Fatal("check status failed", err.Error())
	}
	if len(bytes.TrimSpace(output)) > 0 {
		log.Fatal("unclean working tree")
	}
	// git fetch all tags
	err = runCmd("git", "fetch", "--tags", "--force")
	if err != nil {
		log.Fatal("fetch tags failed", err.Error())
	}
	// get all tags
	output, err = exec.Command("git", "tag").Output()
	if err != nil {
		log.Fatal("list tags failed", err.Error())
	}
	tags := strings.Split(strings.TrimSpace(string(output)), "\n")
	// calc versions
	versions := lo.Map(tags, func(item string, _ int) *semver.Version {
		ver, _ := semver.NewVersion(item)
		return ver
	})
	// get prev semver
	version, _ := semver.NewVersion("v0.0.0")
	if len(versions) > 0 {
		version = lo.MaxBy(versions, func(a, b *semver.Version) bool {
			return a.GreaterThan(b)
		})
	}
	// increase semver based on type
	var next semver.Version
	switch rt := pointer.Get(releaseType); rt {
	case "major":
		next = version.IncMajor()
	case "minor":
		next = version.IncMinor()
	case "patch":
		next = version.IncPatch()
	default:
		log.Fatal("invalid release type", rt)
	}
	// git tag
	err = runCmd("git", "tag", next.Original())
	if err != nil {
		log.Fatal("create tag failed", err.Error())
	}
	// git push all tags
	err = runCmd("git", "push", "--tags")
	if err != nil {
		log.Fatal("push tags failed", err.Error())
	}

	log.Print("next version", next.Original())
	// call go releaser
	err = runCmd("go", "run", "github.com/goreleaser/goreleaser", "release", "--clean")
	if err != nil {
		log.Fatal("failed to call goreleaser")
	}
	// update latest version
	err = runCmd("curl", "-X", "PUT", "-H", "Content-Type: application/octet-stream", "-H", "Access-Key: "+os.Getenv("TOSAccessKey"), "https://wjjb779n.fn.bytedance.net/upload/latest_version.txt", "--data-raw", next.Original())
	if err != nil {
		log.Fatal("failed to update latest version")
	}
}

func runCmd(name string, args ...string) error {
	cmd := exec.Command(name, args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	return cmd.Run()
}
