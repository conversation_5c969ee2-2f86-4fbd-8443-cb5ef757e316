# Staircase - A Stack Diff CLI Tool
# Design
[Stack Diff CLI 架构设计](https://bytedance.larkoffice.com/wiki/M7nGwEJ9zioee2krktfc8fhInAd)
# Release
* Permissions:
  * Maintainer access to this repo
  * Maintainer access to [staircase/tap](https://code.byted.org/staircase/tap), where homebrew tap is hosted
  * Access to the [TOS bucket](https://cloud.bytedance.net/tos/bucket/29552721/overview?region=default)
* Set TOSAccessKey to upload artifacts to TOS 
  * Visit https://cloud.bytedance.net/tos/bucket/29552721/overview?region=default
  * Copy AccessKey
  * Set the previous access key to env: `export TOSAccessKey=${AccessKey}`
* Set private key to push to [staircase/tap](https://code.byted.org/staircase/tap)
  * `export PRIVATE_KEY_PATH=path/to/private_key`
* Release
  * Run `make release release_type=major`, if you make incompatible API changes
  * Run `make release release_type=minor`, if you add functionality in a backward compatible manner
  * Run `make release release_type=patch`, if you make backward compatible bug fixes
