package fxutil

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/require"
)

type fieldStruct1 struct{}

type fieldStruct2 struct{}

type fieldInterface1 interface {
	do()
}

type fieldInterface1Impl struct{}

func (*fieldInterface1Impl) do() {}

type structValid struct {
	F1 *fieldStruct1
	F2 *fieldStruct2
	F3 fieldInterface1
	F4 *fieldStruct1 `fx:"-"`
}

func TestStructConstructorValid(t *testing.T) {
	var want func(*fieldStruct1, *fieldStruct2, fieldInterface1) *structValid
	got := StructConstructor(new(structValid))
	if reflect.TypeOf(got) != reflect.TypeOf(want) {
		t.Fatalf("StructConstructor() = %v, want %v", reflect.TypeOf(got), reflect.TypeOf(want))
	}
	var (
		f1                 = new(fieldStruct1)
		f2                 = new(fieldStruct2)
		f3 fieldInterface1 = new(fieldInterface1Impl)
	)
	v := got.(func(*fieldStruct1, *fieldStruct2, fieldInterface1) *structValid)(f1, f2, f3)
	require.True(t, v.F1 == f1)
	require.True(t, v.F2 == f2)
	require.True(t, v.F3 == f3)
}

type structWithUnexportedField struct {
	f1 *fieldStruct1
}

func TestStructConstructorUnexportedField(t *testing.T) {
	require.Panics(t, func() {
		StructConstructor(new(structWithUnexportedField))
	})
}

type structWithFxIn struct {
	F1 *fieldStruct1 `tag:"1"`
	F2 *fieldStruct2 `name:"hello"`
}

func TestStructWithFxIn(t *testing.T) {
	f := StructConstructor(new(structWithFxIn))
	require.NotNil(t, f)
}
