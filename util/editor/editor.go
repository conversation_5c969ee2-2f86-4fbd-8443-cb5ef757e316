package editor

import (
	"io"
	"os"
	"os/exec"

	"github.com/pkg/errors"
	"gopkg.in/yaml.v3"
	"mvdan.cc/sh/v3/shell"
)

const DefaultEditor = "vi"

func GetEditor() string {
	if editor := os.Getenv("EDITOR"); editor != "" {
		return editor
	}
	return DefaultEditor
}

func RunEditor(filePath string) error {
	editor := GetEditor()

	fields, err := shell.Fields(editor, nil)
	if err != nil {
		return errors.WithMessage(err, "failed to parse editor setting")
	}
	if len(fields) == 0 {
		return errors.New("no fields in editor setting")
	}
	cmd := exec.Command(fields[0], append(fields[1:], filePath)...)
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return cmd.Run()
}

func EditContentWithTempFile(pattern string, content []byte) (newContent []byte, err error) {
	// create a temp file
	f, err := os.CreateTemp("", pattern)
	if err != nil {
		return nil, errors.WithMessage(err, "create temp file failed")
	}
	defer func() {
		_ = f.Close()
		_ = os.Remove(f.Name())
	}()
	// write content to temp file
	_, err = f.Write(content)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to write content to temp file")
	}
	// run editor
	err = RunEditor(f.Name())
	if err != nil {
		return nil, errors.WithMessage(err, "failed to run editor")
	}
	// read yaml file
	f2, err := os.Open(f.Name())
	if err != nil {
		return nil, errors.WithMessage(err, "failed to open temp file")
	}
	defer func() {
		_ = f2.Close()
	}()
	// convert yaml content to change
	newContent, err = io.ReadAll(f2)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to read temp file")
	}
	return newContent, nil
}

func EditYamlWithTempFile[T any](pattern string, value T) (newValue T, err error) {
	content, err := yaml.Marshal(value)
	if err != nil {
		return newValue, errors.WithMessage(err, "failed to marshal value")
	}
	newContent, err := EditContentWithTempFile(pattern, content)
	if err != nil {
		return newValue, errors.WithMessage(err, "failed to edit content")
	}
	err = yaml.Unmarshal(newContent, &newValue)
	if err != nil {
		return newValue, errors.WithMessage(err, "failed to unmarshal value")
	}
	return newValue, nil
}
