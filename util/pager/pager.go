package pager

import (
	"io"
	"os"
	"os/exec"
	"strings"

	"github.com/pkg/errors"
	"mvdan.cc/sh/v3/shell"
)

const DefaultPager = "less"

func GetPager() (name string, args []string) {
	pager := os.Getenv("PAGER")
	if pager == "" {
		return DefaultPager, nil
	}
	fields, err := shell.Fields(pager, nil)
	if err != nil {
		return DefaultPager, nil
	}
	if len(fields) == 0 {
		return DefaultPager, nil
	}
	return fields[0], fields[1:]
}

func Show(s string) error {
	SetupDefaultEnv()
	pagerName, pagerArgs := GetPager()
	cmd := exec.Command(pagerName, pagerArgs...)
	cmd.Stdin = strings.NewReader(s)
	cmd.Stdout = os.Stdout
	return cmd.Run()
}

func Start() (stdin io.WriteCloser, cmd *exec.Cmd, err error) {
	SetupDefaultEnv()
	pagerName, pagerArgs := GetPager()
	cmd = exec.Command(pagerName, pagerArgs...)
	stdin, err = cmd.StdinPipe()
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to get stdin pipe")
	}
	cmd.Stdout = os.Stdout
	err = cmd.Start()
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to start pager")
	}
	return stdin, cmd, nil
}

func SetupDefaultEnv() {
	if os.Getenv("LESS") == "" {
		_ = os.Setenv("LESS", "-R")
	}
}
