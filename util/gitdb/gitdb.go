package gitdb

import (
	"context"
	"os"
	"path"

	"github.com/pkg/errors"
	"gopkg.in/yaml.v3"

	"code.byted.org/vecode/staircase/util/git"
)

type annotationKey struct{}

func WithAnnotation(ctx context.Context, annotation string) context.Context {
	return context.WithValue(ctx, annotationKey{}, annotation)
}

func GetAnnotation(ctx context.Context) string {
	s, _ := ctx.Value(annotationKey{}).(string)
	return s
}

type GitDB[T any] struct {
	Git      git.Git
	RepoPath string
	RefName  string
	FileName string
}

func (d *GitDB[T]) Get(ctx context.Context) (db *T, commitID *string, err error) {
	commit, err := d.Git.GetCommit(ctx, d.RepoPath, d.RefName)
	if errors.Is(err, git.ErrCommitNotFound) {
		return new(T), nil, nil
	} else if err != nil {
		return nil, nil, errors.WithMessage(err, "get commit failed")
	}
	blob, err := d.Git.GetBlob(ctx, d.<PERSON>o<PERSON>ath, commit.ID, d.FileName)
	if errors.Is(err, git.ErrBlobNotFound) {
		blob = nil
	} else if err != nil {
		return nil, nil, errors.WithMessage(err, "get blob failed")
	}
	db = new(T)
	if blob != nil {
		err = yaml.Unmarshal(blob.Content, db)
		if err != nil {
			return nil, nil, errors.WithMessage(err, "unmarshal db failed")
		}
	}
	return db, &commit.ID, nil
}

func (d *GitDB[T]) Modify(ctx context.Context, modifier func(db *T) (string, error)) error {
	db, oldCommitID, err := d.Get(ctx)
	if err != nil {
		return errors.WithMessage(err, "get db failed")
	}
	commitMessage, err := modifier(db)
	if err != nil {
		return errors.WithMessage(err, "failed to modify changeDB")
	}
	if commitMessage == "" {
		commitMessage = GetAnnotation(ctx)
	}
	if commitMessage == "" {
		commitMessage = "dummy"
	}
	newBlobContent, err := yaml.Marshal(db)
	if err != nil {
		return errors.WithMessage(err, "marshal changeDB failed")
	}
	opt := &git.CreateCommitOpt{
		Message: commitMessage,
		Func: func(ctx context.Context, tmpRepoPath string) error {
			file, err := os.Create(path.Join(tmpRepoPath, d.FileName))
			if err != nil {
				return errors.WithMessage(err, "create db file failed")
			}
			_, err = file.Write(newBlobContent)
			if err != nil {
				return errors.WithMessage(err, "write db file failed")
			}
			return nil
		},
	}
	if oldCommitID == nil {
		opt.StartCommitID = git.BlankID
	} else {
		opt.StartCommitID = *oldCommitID
	}
	commit, err := d.Git.CreateCommit(ctx, d.RepoPath, opt)
	if err != nil {
		return errors.WithMessage(err, "create commit failed")
	}
	checkCommitID := git.BlankID
	if oldCommitID != nil {
		checkCommitID = *oldCommitID
	}
	err = d.Git.UpdateRef(ctx, d.RepoPath, d.RefName, commit.ID, &checkCommitID)
	if err != nil {
		return errors.WithMessage(err, "update ref failed")
	}
	return nil
}
