package toposort

import (
	"testing"

	"github.com/stretchr/testify/require"
)

type node struct {
	key   string
	value int
}

func (n node) Key() string {
	return n.key
}

func (n node) Less(other Node) bool {
	return n.value < other.(node).value
}

func TestGraph(t *testing.T) {
	// success
	/*
		a(1) - b(4) - c(1) - d(1)
		     /      /
		e(2)   f(3)
	*/
	g := new(Graph)
	g.<PERSON>d<PERSON>dge(node{"a", 1}, node{"b", 4})
	g.AddEdge(node{"b", 4}, node{"c", 1})
	g.AddEdge(node{"c", 1}, node{"d", 1})
	g.Add<PERSON>dge(node{"e", 2}, node{"b", 4})
	g.Add<PERSON><PERSON>(node{"f", 3}, node{"c", 1})
	got, err := g.Sort()
	require.NoError(t, err)
	require.Equal(t, []Node{
		node{"a", 1},
		node{"e", 2},
		node{"f", 3},
		node{"b", 4},
		node{"c", 1},
		node{"d", 1},
	}, got)
	// cycle
	g = new(Graph)
	g.<PERSON>d<PERSON><PERSON>(node{"a", 1}, node{"b", 1})
	g.AddEdge(node{"b", 1}, node{"a", 1})
	_, err = g.Sort()
	require.ErrorIs(t, ErrorCycle, err)
}
