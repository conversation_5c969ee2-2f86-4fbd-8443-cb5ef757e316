package toposort

import (
	"errors"

	"github.com/samber/lo"

	"code.byted.org/vecode/staircase/util/priorityqueue"
)

var ErrorCycle = errors.New("cycle detected")

type Node interface {
	Key() string
	Less(other Node) bool
}

type item struct {
	Node
	toKeys   []string
	inDegree int
}

func (t *item) Less(other priorityqueue.Node) bool {
	otherT := other.(*item)
	if t.inDegree < otherT.inDegree {
		return true
	} else if t.inDegree == otherT.inDegree {
		return t.Node.Less(otherT.Node)
	} else {
		return false
	}
}

type Graph struct {
	items map[string]*item
}

func (g *Graph) AddNode(n Node) {
	if g.items == nil {
		g.items = make(map[string]*item)
	}
	if _, ok := g.items[n.Key()]; ok {
		return
	}
	g.items[n.Key()] = &item{Node: n}
}

func (g *Graph) AddEdge(from Node, to Node) {
	g.AddNode(from)
	g.AddNode(to)

	fromItem := g.items[from.Key()]
	if lo.Contains(fromItem.toKeys, to.Key()) {
		return
	}
	fromItem.toKeys = append(fromItem.toKeys, to.Key())

	toItem := g.items[to.Key()]
	toItem.inDegree++
}

func (g *Graph) Sort() ([]Node, error) {
	pq := new(priorityqueue.PriorityQueue)
	for _, item := range g.items {
		pq.Push(item)
	}
	result := make([]Node, 0, len(g.items))
	for range g.items {
		node, ok := pq.Pop()
		if !ok {
			return nil, errors.New("pop failed")
		}
		t := node.(*item)
		if t.inDegree > 0 {
			return nil, ErrorCycle
		}
		result = append(result, t.Node)
		for _, toKey := range t.toKeys {
			toItem := g.items[toKey]
			toItem.inDegree--
			pq.Update(toItem)
		}
	}
	_, ok := pq.Pop()
	if ok {
		return nil, errors.New("queue not empty")
	}
	return result, nil
}
