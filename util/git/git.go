package git

import (
	"context"
	"io"
	"net/url"
	"path/filepath"
	"strings"
	"time"

	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	giturls "github.com/whilp/git-urls"
)

var (
	ErrNotSymbolicRef   = errors.New("not a symbolic ref")
	ErrBranchNotFound   = errors.New("branch not found")
	ErrCommitNotFound   = errors.New("commit not found")
	ErrBlobNotFound     = errors.New("blob not found")
	ErrConflict         = errors.New("conflict")
	ErrRevisionNotFound = errors.New("revision not found")
	ErrRemoteNotFound   = errors.New("remote not found")
	ErrConfigNotFound   = errors.New("config not found")
)

const (
	BlankID       = "0000000000000000000000000000000000000000"
	EmptyTreeID   = "4b825dc642cb6eb9a060e54bf8d69288fbee4904"
	DefaultRemote = "origin"
	DefaultBranch = "main"
)

type Git interface {
	IsWorktreeClean(ctx context.Context, path string) (bool, error)
	GetPathFromRepoRoot(ctx context.Context, path string) (string, error)
	GetWorkTree(ctx context.Context, path string) (string, error)
	GetGitDir(ctx context.Context, path string) (string, error)
	GetCurrentBranch(ctx context.Context, repoPath string) (string, error)
	GetCurrentRebaseBranchName(ctx context.Context, repoPath string) (string, error)
	GetHEADCommitID(ctx context.Context, repoPath string) (string, error)
	GetDefaultBranchName(ctx context.Context, repoPath, remote string) (string, error)
	CreateBranch(ctx context.Context, repoPath, branchName, commitID string) error
	Checkout(ctx context.Context, repoPath, branchName string, force bool) error
	GetMergeBase(ctx context.Context, repoPath string, revisions ...string) (string, error)
	GetCommit(ctx context.Context, repoPath string, revision string) (*Commit, error)
	GetBlob(ctx context.Context, repoPath string, revision string, path string) (*Blob, error)
	CreateCommit(ctx context.Context, repoPath string, opt *CreateCommitOpt) (*Commit, error)
	UpdateRef(ctx context.Context, repoPath string, ref string, commitID string, oldCommitID *string) error
	UpdateRefs(ctx context.Context, opt *UpdateRefsOpt) error
	GetBranch(ctx context.Context, repoPath string, branchName string) (*Branch, error)
	GetRemote(ctx context.Context, repoPath string, remoteName string) (remote *Remote, err error)
	ListRemoteNames(ctx context.Context, repoPath string) ([]string, error)
	Push(ctx context.Context, opt *PushOpt) error
	GetRepoConfig(ctx context.Context, repoPath string, key string) (string, error)
	UpdateRepoConfig(ctx context.Context, repoPath string, key string, value string) error
	UnsetRepoConfig(ctx context.Context, repoPath string, key string) error
	Rebase(ctx context.Context, repoPath string, opt *RebaseOpt) (string, error)
	ManualRebase(ctx context.Context, repoPath string, opt *RebaseOpt) error
	ManualRebaseContinue(ctx context.Context, repoPath string, opt *ManualRebaseContinueOpt) error
	ManualRebaseAbort(ctx context.Context, repoPath string) error
	Reset(ctx context.Context, repoPath string, opt *ResetOpt) error
	Fetch(ctx context.Context, opt *FetchOpt) error
	ListRemoteReferences(ctx context.Context, repoPath string, remote string, patterns ...string) ([]Reference, error)
	CommitTree(ctx context.Context, opt *CommitTreeOpt) (commitID string, err error)
	Squash(ctx context.Context, opt *SquashOpt) (commitID string, err error)
	ListCommits(ctx context.Context, opt *ListCommitsOpt) ([]*Commit, error)
	ListDiffFilePaths(ctx context.Context, opt *ListDiffFilePathsOpt) ([]string, error)
	CreateWorkTree(ctx context.Context, opt *CreateWorkTreeOpt) (worktree string, clean func() error, err error)
	ManualAdd(ctx context.Context, opt *ManualAddOpt) error
	WriteTree(ctx context.Context, repoPath string) (string, error)
	ReadTree(ctx context.Context, opt *ReadTreeOpt) error
	GetWorktreeStatus(ctx context.Context, path string) (*WorktreeStatus, error)
	PrepareTreesForRangeDiff(ctx context.Context, opt *PrepareTreesForRangeDiffOpt) (fromRevision, toRevision string, err error)
}

type PrepareTreesForRangeDiffOpt struct {
	RepoPath        string
	OldFromRevision string
	OldToRevision   string
	NewFromRevision string
	NewToRevision   string
}

type FetchOpt struct {
	RepoPath       string
	Remote         string
	RefSpecs       []string
	ProgressWriter io.Writer
}

type PushOpt struct {
	RepoPath       string
	Remote         string
	RefSpecs       []string
	PushOptions    []string
	ProgressWriter io.Writer
}

type UpdateRefsOpt struct {
	RepoPath   string
	RefUpdates []RefUpdate
}

type RefUpdate struct {
	RefName     string
	NewCommitID string
	OldCommitID *string
}

type ReadTreeOpt struct {
	RepoPath       string
	Revision       string
	Merge          bool // Whether to perform a merge, this is useful in sparse checkout
	UpdateWorkTree bool
}

type ManualAddOpt struct {
	RepoPath string
	Stdin    io.Reader
	Stdout   io.Writer
	Stderr   io.Writer
}

type CreateWorkTreeOpt struct {
	RepoPath                string
	Revision                string
	SparseCheckoutFilePaths *[]string
}

type ListDiffFilePathsOpt struct {
	RepoPath     string
	FromRevision string
	FromIndex    bool
	ToRevision   string
	ToWorkTree   bool
	ToIndex      bool
	UseMergeBase bool
}

type ListCommitsOpt struct {
	RepoPath    string
	Revision    string
	FirstParent bool
	Reverse     bool
}

type SquashOpt struct {
	RepoPath      string
	Base          string
	End           string
	CommitMessage string
}

type CommitTreeOpt struct {
	RepoPath        string
	Revision        string
	Message         string
	ParentRevisions []string
	Author          *Signature
	Committer       *Signature
}

type Reference struct {
	Name     RefName
	CommitID string
}

type ManualRebaseContinueOpt struct {
	Stdin  io.Reader
	Stdout io.Writer
	Stderr io.Writer
}

type ResetOpt struct {
	// If Revision is empty, "HEAD" will be used
	Revision string
	// Whether to reset working tree
	WorkingTree bool
	// Only valid if WorkingTree is false
	MarkRemovedPathsAsIntentToAdd bool
}

type RebaseOpt struct {
	NewBase    string
	Base       string
	End        string
	Squash     bool
	AutoSquash bool
	// Must be non-nil if Squash is true
	SquashCommitMessage *string
}

type CreateCommitOpt struct {
	Message       string
	WithIndex     bool
	StartCommitID string
	Func          func(ctx context.Context, tmpRepoPath string) error
}

type Branch struct {
	Name   string
	Commit *Commit
}

type Signature struct {
	Name  string
	Email string
	When  time.Time
}

type Commit struct {
	ID        string
	Message   string
	Author    *Signature
	Committer *Signature
	ParentIDs []string
	TreeID    string
}

func (c *Commit) Subject() string {
	message := strings.TrimSpace(c.Message)
	firstLF := strings.Index(message, "\n")
	if firstLF == -1 {
		return message
	}
	return strings.TrimSpace(message[:firstLF])
}

func (c *Commit) FixUp() string {
	subject := c.Subject()
	prefix := "fixup! "
	if strings.HasPrefix(subject, prefix) {
		return strings.TrimPrefix(subject, prefix)
	}
	return ""
}

type Blob struct {
	ID      string
	Content []byte
}

type Remote struct {
	Name    string
	URL     string
	PushURL string
}

func (r Remote) MainURL() string {
	if r.PushURL != "" {
		return r.PushURL
	}
	return r.URL
}

func (r Remote) MainRepoPath() (string, error) {
	u, err := ParseRemoteURL(r.MainURL())
	if err != nil {
		return "", err
	}
	return strings.TrimSuffix(strings.Trim(u.Path, "/"), ".git"), nil
}

func ParseRemoteURL(url string) (*url.URL, error) {
	return giturls.Parse(url)
}

const BranchRefPrefix = "refs/heads/"

type RefName string

func (r RefName) String() string {
	return string(r)
}

func (r RefName) IsBranch() bool {
	return strings.HasPrefix(string(r), BranchRefPrefix)
}

func (r RefName) BranchName() string {
	return strings.TrimPrefix(string(r), BranchRefPrefix)
}

type TrackedEntry struct {
	Path              string
	Unmerged          bool
	IndexStatus       *byte
	WorkingTreeStatus *byte
	OriginalPath      *string
	OurStatus         *byte
	TheirStatus       *byte
}

func (e *TrackedEntry) IndexChanged() bool {
	return !e.Unmerged && e.IndexStatus != nil && *e.IndexStatus != '.'
}

func (e *TrackedEntry) WorkingTreeChanged() bool {
	return !e.Unmerged && e.WorkingTreeStatus != nil && *e.WorkingTreeStatus != '.'
}

func (e *TrackedEntry) IndexChangeString() string {
	if !e.IndexChanged() {
		return ""
	}
	result := statusToString(*e.IndexStatus)
	result += ": "
	if e.OriginalPath != nil {
		result += *e.OriginalPath
		result += " -> "
	}
	result += e.Path
	return result
}

func (e *TrackedEntry) WorkingTreeChangeString() string {
	if !e.WorkingTreeChanged() {
		return ""
	}
	return statusToString(*e.WorkingTreeStatus) + ": " + e.Path
}

func (e *TrackedEntry) UnmergedChangeString() string {
	if !e.Unmerged {
		return ""
	}
	return conflictStatusToString(*e.OurStatus, *e.TheirStatus) + ": " + e.Path
}

func conflictStatusToString(ourStatus, theirStatus byte) string {
	switch string([]byte{ourStatus, theirStatus}) {
	case "DD":
		return "both deleted"
	case "AU":
		return "added by us"
	case "UD":
		return "deleted by them"
	case "UA":
		return "added by them"
	case "DU":
		return "deleted by us"
	case "AA":
		return "both added"
	case "UU":
		return "both modified"
	default:
		return "unknown"
	}
}

func statusToString(status byte) string {
	switch status {
	case 'A':
		return "new file"
	case 'C':
		return "copied"
	case 'D':
		return "deleted"
	case 'M':
		return "modified"
	case 'R':
		return "renamed"
	case 'T':
		return "type change"
	case 'X':
		return "unknown"
	case 'U':
		return "unmerged"
	default:
		return "unknown"
	}
}

type WorktreeStatus struct {
	CurrentCommitID   *string
	CurrentBranchName *string
	TrackedEntries    []*TrackedEntry
	UntrackedPaths    []string
	RebaseInProgress  bool
}

func (s *WorktreeStatus) IsClean() bool {
	return len(s.TrackedEntries) == 0 && len(s.UntrackedPaths) == 0
}

func (s *WorktreeStatus) HasStagedChanges() bool {
	for _, entry := range s.TrackedEntries {
		if entry.IndexChanged() {
			return true
		}
	}
	return false
}

func (s *WorktreeStatus) Rel(basePath string) *WorktreeStatus {
	for _, entry := range s.TrackedEntries {
		entry.Path = s.filepathRel(basePath, entry.Path)
		if entry.OriginalPath != nil {
			entry.OriginalPath = pointer.To(s.filepathRel(basePath, *entry.OriginalPath))
		}
	}
	s.UntrackedPaths = lo.Map(s.UntrackedPaths, func(item string, _ int) string {
		return s.filepathRel(basePath, item)
	})
	return s
}

func (s *WorktreeStatus) filepathRel(basePath, targetPath string) string {
	p, err := filepath.Rel(basePath, targetPath)
	if err != nil {
		return targetPath
	}
	return p
}
