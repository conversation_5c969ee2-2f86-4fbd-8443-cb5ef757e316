package impl

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"io/fs"
	"os"
	"os/exec"
	"path"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/multierr"

	"code.byted.org/vecode/staircase/util/git"
)

var _ git.Git = new(Git)

const worktreeDir = "staircase-worktrees"

type Git struct{}

func (g *Git) PrepareTreesForRangeDiff(ctx context.Context, opt *git.PrepareTreesForRangeDiffOpt) (fromRevision, toRevision string, err error) {
	toRevision, err = g.runGit(ctx, opt.RepoPath, runGitOption{
		Args: []string{"merge-tree", "--allow-unrelated-histories", "--write-tree", "--strategy-option", "theirs", "--merge-base=" + opt.NewFromRevision, opt.OldToRevision, opt.NewToRevision},
	})
	if ee := new(exec.ExitError); errors.As(err, &ee) && ee.ExitCode() == 1 { // conflict
		// fallback to direct diff
		return opt.OldToRevision, opt.NewToRevision, nil
	}
	if err != nil {
		return "", "", errors.WithMessage(err, "failed to merge tree")
	}
	return opt.OldToRevision, toRevision, nil
}

func (g *Git) UpdateRefs(ctx context.Context, opt *git.UpdateRefsOpt) error {
	if len(opt.RefUpdates) == 0 {
		return nil
	}
	buf := &bytes.Buffer{}
	buf.WriteString("start\n")
	for _, update := range opt.RefUpdates {
		if update.OldCommitID == nil {
			buf.WriteString(fmt.Sprintf("update %s %s\n", update.RefName, update.NewCommitID))
		} else {
			buf.WriteString(fmt.Sprintf("update %s %s %s\n", update.RefName, update.NewCommitID, *update.OldCommitID))
		}
	}
	buf.WriteString("commit\n")

	_, err := g.runGit(ctx, opt.RepoPath, runGitOption{
		Args:  []string{"update-ref", "--stdin"},
		Stdin: buf,
	})
	return err
}

func (g *Git) ReadTree(ctx context.Context, opt *git.ReadTreeOpt) error {
	args := []string{"read-tree"}
	if opt.Merge {
		args = append(args, "-m")
	}
	if opt.UpdateWorkTree {
		args = append(args, "-u")
	}
	args = append(args, opt.Revision)
	_, err := g.runGit(ctx, opt.RepoPath, runGitOption{
		Args: args,
	})
	return err
}

func (g *Git) WriteTree(ctx context.Context, repoPath string) (string, error) {
	treeID, err := g.runGit(ctx, repoPath, runGitOption{
		Args: []string{"write-tree"},
	})
	if err != nil {
		return "", err
	}
	return treeID, nil
}

func (g *Git) ManualAdd(ctx context.Context, opt *git.ManualAddOpt) error {
	_, err := g.runGit(ctx, opt.RepoPath, runGitOption{
		Args:   []string{"add", "-p"},
		Stdin:  opt.Stdin,
		Stdout: opt.Stdout,
		Stderr: opt.Stderr,
	})
	return err
}

func (g *Git) CreateWorkTree(ctx context.Context, opt *git.CreateWorkTreeOpt) (worktree string, clean func() error, err error) {
	revision := opt.Revision
	repoPath := opt.RepoPath
	// create a commit with an empty tree if needed
	if revision == git.BlankID {
		// create an empty commit
		stdout, err := g.runGit(ctx, repoPath, runGitOption{Args: []string{"commit-tree", "-m", "empty commit", git.EmptyTreeID}})
		if err != nil {
			return "", nil, errors.WithMessage(err, "failed to create empty commit")
		}
		revision = stdout
	}
	// get .git dir
	gitDir, err := g.GetGitDir(ctx, repoPath)
	if err != nil {
		return "", nil, errors.WithMessage(err, "failed to get git dir")
	}
	// calc worktree path
	worktreeName := fmt.Sprint(time.Now().Nanosecond())
	worktreePath := path.Join(gitDir, worktreeDir, worktreeName)
	// create worktree
	createArgs := []string{"worktree", "add", "--detach"}
	if opt.SparseCheckoutFilePaths != nil {
		createArgs = append(createArgs, "--no-checkout")
	}
	createArgs = append(createArgs, worktreePath, revision)
	_, err = g.runGit(ctx, repoPath, runGitOption{Args: createArgs})
	if err != nil {
		return "", nil, errors.WithMessage(err, "failed to create worktree")
	}
	clean = func() error {
		_, err := g.runGit(ctx, repoPath, runGitOption{Args: []string{"worktree", "remove", "-f", worktreePath}})
		return err
	}
	defer func() {
		if err != nil {
			err = multierr.Combine(err, clean())
		}
	}()
	// set up worktree
	if opt.SparseCheckoutFilePaths != nil {
		// configure sparse checkout settings
		_, err = g.runGit(ctx, worktreePath, runGitOption{
			Args:  []string{"sparse-checkout", "set", "--no-cone", "--stdin"},
			Stdin: bytes.NewBufferString(strings.Join(*opt.SparseCheckoutFilePaths, "\n")),
		})
		if err != nil {
			return "", nil, errors.WithMessage(err, "failed to init sparse-checkout")
		}
		// checkout worktree
		_, err = g.runGit(ctx, worktreePath, runGitOption{Args: []string{"checkout", "--detach"}})
		if err != nil {
			return "", nil, errors.WithMessage(err, "failed to checkout worktree")
		}
	}
	return worktreePath, clean, nil
}

func (g *Git) ListDiffFilePaths(ctx context.Context, opt *git.ListDiffFilePathsOpt) ([]string, error) {
	args := []string{"diff", "--name-only"}
	if opt.UseMergeBase {
		args = append(args, "--merge-base")
	}
	switch {
	case opt.FromRevision != "" && opt.ToRevision != "":
		args = append(args, opt.FromRevision, opt.ToRevision)
	case opt.FromIndex && opt.ToWorkTree:
	case opt.FromRevision != "" && opt.ToWorkTree:
		args = append(args, opt.FromRevision)
	case opt.FromRevision != "" && opt.ToIndex:
		args = append(args, "--cached", opt.FromRevision)
	default:
		return nil, errors.New("invalid options")
	}
	output, err := g.runGit(ctx, opt.RepoPath, runGitOption{
		Args: args,
	})
	if err != nil {
		return nil, err
	}
	if output == "" {
		return nil, nil
	}
	return strings.Split(output, "\n"), nil
}

func (g *Git) IsWorktreeClean(ctx context.Context, path string) (bool, error) {
	output, err := g.runGit(ctx, path, runGitOption{
		Args: []string{"status", "--porcelain"},
	})
	if err != nil {
		return false, err
	}
	return output == "", nil
}

func (g *Git) CommitTree(ctx context.Context, opt *git.CommitTreeOpt) (commitID string, err error) {
	args := []string{"commit-tree", opt.Revision + "^{tree}", "-m", opt.Message}
	for _, pr := range opt.ParentRevisions {
		args = append(args, "-p")
		args = append(args, pr)
	}
	var env []string
	if opt.Author != nil {
		when := opt.Author.When
		if when.IsZero() {
			when = time.Now()
		}
		env = append(env, fmt.Sprintf("GIT_AUTHOR_NAME=%s", opt.Author.Name))
		env = append(env, fmt.Sprintf("GIT_AUTHOR_EMAIL=%s", opt.Author.Email))
		env = append(env, fmt.Sprintf("GIT_AUTHOR_DATE=%s", when))
	}
	if opt.Committer != nil {
		when := opt.Committer.When
		if when.IsZero() {
			when = time.Now()
		}
		env = append(env, fmt.Sprintf("GIT_COMMITTER_NAME=%s", opt.Committer.Name))
		env = append(env, fmt.Sprintf("GIT_COMMITTER_EMAIL=%s", opt.Committer.Email))
		env = append(env, fmt.Sprintf("GIT_COMMITTER_DATE=%s", when))
	}
	return g.runGit(ctx, opt.RepoPath, runGitOption{Args: args, Env: env})
}

func (g *Git) ListRemoteReferences(ctx context.Context, repoPath string, remote string, patterns ...string) ([]git.Reference, error) {
	if len(patterns) == 0 {
		return nil, nil
	}
	output, err := g.runGit(ctx, repoPath, runGitOption{Args: append([]string{"ls-remote", remote}, patterns...)})
	if err != nil {
		return nil, err
	}
	if len(output) == 0 {
		return nil, nil
	}
	var result []git.Reference
	for _, line := range strings.Split(output, "\n") {
		line = strings.TrimSpace(line)
		commitID, refName, ok := strings.Cut(line, "\t")
		if !ok {
			return nil, errors.Errorf("invalid ls-remote output: %s", line)
		}
		result = append(result, git.Reference{
			Name:     git.RefName(refName),
			CommitID: commitID,
		})
	}
	return result, nil
}

func (g *Git) ManualRebaseContinue(ctx context.Context, repoPath string, opt *git.ManualRebaseContinueOpt) error {
	_, err := g.runGit(ctx, repoPath, runGitOption{
		Args:   []string{"rebase", "--continue"},
		Stdin:  opt.Stdin,
		Stdout: opt.Stdout,
		Stderr: opt.Stderr,
	})
	if ee := new(exec.ExitError); errors.As(err, &ee) && ee.ExitCode() == 1 {
		return git.ErrConflict
	}
	return err
}

func (g *Git) ManualRebaseAbort(ctx context.Context, repoPath string) error {
	_, err := g.runGit(ctx, repoPath, runGitOption{Args: []string{"rebase", "--abort"}})
	return err
}

func (g *Git) Reset(ctx context.Context, repoPath string, opt *git.ResetOpt) error {
	args := []string{"reset"}
	if opt.WorkingTree {
		args = append(args, "--hard")
	} else {
		args = append(args, "--mixed")
		if opt.MarkRemovedPathsAsIntentToAdd {
			args = append(args, "-N")
		}
	}
	if opt.Revision != "" {
		args = append(args, opt.Revision)
	}
	_, err := g.runGit(ctx, repoPath, runGitOption{Args: args})
	return err
}

func (g *Git) Fetch(ctx context.Context, opt *git.FetchOpt) error {
	if len(opt.RefSpecs) == 0 {
		return nil
	}
	_, err := g.runGit(ctx, opt.RepoPath, runGitOption{
		Args:   append([]string{"fetch", opt.Remote}, opt.RefSpecs...),
		Stderr: opt.ProgressWriter,
	})
	return err
}

func (g *Git) GetCurrentRebaseBranchName(ctx context.Context, repoPath string) (string, error) {
	dirPath, err := g.runGit(ctx, repoPath, runGitOption{Args: []string{"rev-parse", "--git-path", "rebase-merge"}})
	if err != nil {
		return "", err
	}

	content, err := os.ReadFile(path.Join(dirPath, "head-name"))
	if err == nil {
		return git.RefName(bytes.TrimSpace(content)).BranchName(), nil
	}
	return "", git.ErrBranchNotFound
}

func (g *Git) Rebase(ctx context.Context, repoPath string, opt *git.RebaseOpt) (string, error) {
	baseOID, err := g.getOIDFromRevision(ctx, repoPath, opt.Base+"^{commit}")
	if err != nil {
		return "", errors.WithMessage(err, "failed to get base oid")
	}
	endOID, err := g.getOIDFromRevision(ctx, repoPath, opt.End+"^{commit}")
	if err != nil {
		return "", errors.WithMessage(err, "failed to get end oid")
	}
	if opt.Squash {
		endOID, err = g.Squash(ctx, &git.SquashOpt{
			RepoPath:      repoPath,
			Base:          baseOID,
			End:           endOID,
			CommitMessage: *opt.SquashCommitMessage,
		})
		if err != nil {
			return "", errors.WithMessage(err, "failed to create squash commit")
		}
	}
	return g.rebaseUsingMergeTree(ctx, repoPath, baseOID, endOID, opt.NewBase, opt.AutoSquash)
}

func (g *Git) getOIDFromRevision(ctx context.Context, repoPath string, revision string) (string, error) {
	oid, err := g.runGit(ctx, repoPath, runGitOption{Args: []string{"rev-parse", "--verify", revision}})
	if ee := new(exec.ExitError); errors.As(err, &ee) && ee.ExitCode() == 128 {
		return "", git.ErrRevisionNotFound
	}
	return oid, err
}

func (g *Git) rebaseUsingMergeTree(
	ctx context.Context, repoPath, baseOID, endOID, newBase string, autoSquash bool,
) (string, error) {
	// list commits that are
	// 1. reachable from endOID
	// 2. and not reachable from newBase
	// 3. and not patch-equivalent to commits from newBase
	stdout, err := g.runGit(ctx, repoPath, runGitOption{Args: []string{
		"rev-list",
		// Flags of git-rev-list to get the pick-only todo_list for a rebase.
		// Currently, we drop clean cherry-picks and merge commits, which is
		// also what git2go does.
		// The flags are inferred from https://github.com/git/git/blob/v2.41.0/sequencer.c#L5704-L5714
		"--cherry-pick",
		"--right-only",
		"--no-merges",
		"--topo-order",
		"--reverse",
		// The notation "<upstream>...<branch>" is used to calculate the symmetric
		// difference between upstream and branch. It will return the commits that
		// are reachable exclusively from either side but not both. Combined with
		// the provided --right-only flag, the result should be only commits which
		// exist on the branch that is to be rebased.
		fmt.Sprintf("%s...%s", newBase, endOID),
	}})
	if err != nil {
		return "", errors.WithMessage(err, "failed to list commits")
	}
	commitIDs := lo.WithoutEmpty(strings.Split(stdout, "\n"))
	// list commits that are reachable from endOID but not reachable from baseOID
	stdout, err = g.runGit(ctx, repoPath, runGitOption{Args: []string{
		"rev-list",
		fmt.Sprintf("%s..%s", baseOID, endOID),
	}})
	if err != nil {
		return "", errors.WithMessage(err, "failed to list commits in range")
	}
	commitIDsInRange := lo.WithoutEmpty(strings.Split(stdout, "\n"))
	// get intersection
	commitIDs = lo.Filter(commitIDs, func(todo string, _ int) bool {
		return lo.Contains(commitIDsInRange, todo)
	})
	// get commits
	commitsByID := make(map[string]*git.Commit, len(commitIDs))
	for _, commitID := range commitIDs {
		commit, err := g.GetCommit(ctx, repoPath, commitID)
		if err != nil {
			return "", errors.WithMessage(err, "failed to get commit")
		}
		commitsByID[commitID] = commit
	}
	// group by commit message if necessary
	commitGroups := make([][]*git.Commit, 0, len(commitIDs))
	for _, commitID := range commitIDs {
		commit := commitsByID[commitID]
		if !autoSquash {
			commitGroups = append(commitGroups, []*git.Commit{commit})
			continue
		}
		specifier := commit.FixUp()
		if specifier == "" {
			commitGroups = append(commitGroups, []*git.Commit{commit})
			continue
		}
		_, i, _ := lo.FindLastIndexOf(commitGroups, func(item []*git.Commit) bool {
			return item[0].Subject() == specifier
		})
		if i < 0 {
			commitGroups = append(commitGroups, []*git.Commit{commit})
			continue
		}
		commitGroups[i] = append(commitGroups[i], commit)
	}
	commits := lo.Flatten(commitGroups)

	newBaseCommit, err := g.GetCommit(ctx, repoPath, newBase)
	if err != nil {
		return "", errors.WithMessage(err, "failed to get new base commit")
	}

	ourCommitID := newBaseCommit.ID
	ourTreeID := newBaseCommit.TreeID
	for _, commit := range commits {
		args := []string{
			"merge-tree",
			"--write-tree",
			"-z",
			"--allow-unrelated-histories",
		}

		if len(commit.ParentIDs) > 0 {
			args = append(args, fmt.Sprintf("--merge-base=%s", commit.ParentIDs[0]))
		}

		args = append(args, ourCommitID, commit.ID)

		stdout, err = g.runGit(ctx, repoPath, runGitOption{
			Args: args,
		})
		if err != nil {
			if ee := new(exec.ExitError); errors.As(err, &ee) && ee.ExitCode() == 1 {
				return "", git.ErrConflict
			}
			return "", errors.WithMessage(err, "failed to call merge-tree")
		}
		newTreeOID := strings.Trim(stdout, "\x00\n")

		if autoSquash && commit.FixUp() != "" {
			if newTreeOID == ourTreeID {
				continue
			}
			ourCommit, err := g.GetCommit(ctx, repoPath, ourCommitID)
			if err != nil {
				return "", errors.WithMessage(err, "failed to get our commit")
			}
			newCommitID, err := g.CommitTree(ctx, &git.CommitTreeOpt{
				RepoPath:        repoPath,
				Revision:        newTreeOID,
				Message:         ourCommit.Message,
				ParentRevisions: ourCommit.ParentIDs,
				Author:          ourCommit.Author,
				Committer:       ourCommit.Committer,
			})
			if err != nil {
				return "", errors.WithMessage(err, "failed to create commit")
			}

			ourCommitID = newCommitID
			ourTreeID = newTreeOID

			continue
		}

		// When no tree changes detected, we need to further check
		// 1. if the commit itself introduces no changes, pick it anyway.
		// 2. if the commit is not empty to start and is not clean cherry-picks of any
		//    upstream commit, but become empty after rebasing, we just ignore it.
		// Refer to https://git-scm.com/docs/git-rebase#Documentation/git-rebase.txt---emptydropkeepask
		if newTreeOID == ourTreeID {
			if len(commit.ParentIDs) == 0 {
				if commit.TreeID != git.EmptyTreeID {
					continue
				}
			} else {
				theirsParentCommit, err := g.GetCommit(ctx, repoPath, commit.ParentIDs[0])
				if err != nil {
					return "", errors.WithMessage(err, "failed to get theirs parent commit")
				}

				if theirsParentCommit.TreeID != commit.TreeID {
					continue
				}
			}
		}

		newCommitID, err := g.CommitTree(ctx, &git.CommitTreeOpt{
			RepoPath:        repoPath,
			Revision:        newTreeOID,
			Message:         commit.Message,
			ParentRevisions: []string{ourCommitID},
			Author:          commit.Author,
			Committer:       commit.Committer,
		})
		if err != nil {
			return "", errors.WithMessage(err, "failed to create commit")
		}

		ourCommitID = newCommitID
		ourTreeID = newTreeOID
	}

	return ourCommitID, nil
}

func (g *Git) ManualRebase(ctx context.Context, repoPath string, opt *git.RebaseOpt) error {
	var (
		end = opt.End
		err error
	)
	if opt.Squash {
		end, err = g.Squash(ctx, &git.SquashOpt{
			RepoPath:      repoPath,
			Base:          opt.Base,
			End:           end,
			CommitMessage: *opt.SquashCommitMessage,
		})
		if err != nil {
			return errors.WithMessage(err, "failed to create squash commit")
		}
	}
	_, err = g.runGit(ctx, repoPath, runGitOption{Args: []string{"rebase", "--onto", opt.NewBase, opt.Base, end}})
	return err
}

func (g *Git) GetWorkTree(ctx context.Context, path string) (string, error) {
	stdout, err := g.runGit(ctx, path, runGitOption{Args: []string{"rev-parse", "--show-toplevel"}})
	if err != nil {
		return "", err
	}
	return stdout, nil
}

func (g *Git) GetPathFromRepoRoot(ctx context.Context, path string) (string, error) {
	stdout, err := g.runGit(ctx, path, runGitOption{Args: []string{"rev-parse", "--show-prefix"}})
	if err != nil {
		return "", err
	}
	return stdout, nil
}

func (g *Git) GetGitDir(ctx context.Context, path string) (string, error) {
	stdout, err := g.runGit(ctx, path, runGitOption{Args: []string{"rev-parse", "--absolute-git-dir"}})
	if err != nil {
		return "", err
	}
	return stdout, nil
}

func (g *Git) withWorkTree(ctx context.Context, repoPath, worktreeName, revision string, f func(workTreePath string) error) error {
	if revision == git.BlankID {
		// create an empty commit
		stdout, err := g.runGit(ctx, repoPath, runGitOption{Args: []string{"commit-tree", "-m", "empty commit", git.EmptyTreeID}})
		if err != nil {
			return errors.WithMessage(err, "failed to create empty commit")
		}
		revision = stdout
	}
	gitDir, err := g.GetGitDir(ctx, repoPath)
	if err != nil {
		return errors.WithMessage(err, "failed to get git dir")
	}
	worktreePath := path.Join(gitDir, worktreeDir, worktreeName)
	_, err = g.runGit(ctx, repoPath, runGitOption{Args: []string{"worktree", "add", "--detach", worktreePath, revision}})
	if err != nil {
		return errors.WithMessage(err, "failed to create worktree")
	}
	defer func() {
		_, _ = g.runGit(ctx, repoPath, runGitOption{Args: []string{"worktree", "remove", "-f", worktreePath}})
	}()

	return f(worktreePath)
}

func (g *Git) getSymbolicRef(ctx context.Context, repoPath, ref string) (git.RefName, error) {
	stdout, err := g.runGit(ctx, repoPath, runGitOption{Args: []string{"symbolic-ref", ref}})
	if ee := new(exec.ExitError); errors.As(err, &ee) {
		if bytes.Contains(ee.Stderr, []byte("is not a symbolic ref")) {
			return "", git.ErrNotSymbolicRef
		}
		return "", err
	} else if err != nil {
		return "", err
	}
	return git.RefName(stdout), nil
}

func (g *Git) GetCurrentBranch(ctx context.Context, repoPath string) (string, error) {
	refTarget, err := g.getSymbolicRef(ctx, repoPath, "HEAD")
	if errors.Is(err, git.ErrNotSymbolicRef) {
		return "", git.ErrBranchNotFound
	} else if err != nil {
		return "", err
	}
	if !refTarget.IsBranch() {
		return "", git.ErrBranchNotFound
	}
	return refTarget.BranchName(), nil
}

func (g *Git) GetHEADCommitID(ctx context.Context, repoPath string) (string, error) {
	return g.runGit(ctx, repoPath, runGitOption{Args: []string{"rev-parse", "HEAD"}})
}

func (g *Git) GetDefaultBranchName(ctx context.Context, repoPath, remote string) (string, error) {
	// fixme: this may not be right
	if remote == "" {
		remote = git.DefaultRemote
	}
	refTarget, err := g.getSymbolicRef(ctx, repoPath, fmt.Sprintf("refs/remotes/%s/HEAD", remote))
	if errors.Is(err, git.ErrNotSymbolicRef) {
		return git.DefaultBranch, nil
	} else if err != nil {
		return "", err
	}
	return strings.TrimPrefix(string(refTarget), fmt.Sprintf("refs/remotes/%s/", remote)), nil
}

func (g *Git) CreateBranch(ctx context.Context, repoPath, branchName, commitID string) error {
	return g.UpdateRef(ctx, repoPath, "refs/heads/"+branchName, commitID, nil)
}

func (g *Git) MoveBranch(ctx context.Context, repoPath, oldBranchName, newBranchName string) error {
	_, err := g.runGit(ctx, repoPath, runGitOption{
		Args: []string{"branch", "-m", oldBranchName, newBranchName},
	})
	return err
}

func (g *Git) Checkout(ctx context.Context, repoPath, branchName string, force bool) error {
	args := []string{"checkout", branchName}
	if force {
		args = append(args, "-f")
	}
	_, err := g.runGit(ctx, repoPath, runGitOption{Args: args})
	return err
}

func (g *Git) GetMergeBase(ctx context.Context, repoPath string, revisions ...string) (string, error) {
	args := []string{"merge-base"}
	for _, revision := range revisions {
		args = append(args, revision)
	}
	stdout, err := g.runGit(ctx, repoPath, runGitOption{Args: args})
	if err != nil {
		return "", err
	}
	return stdout, nil
}

func (g *Git) GetCommit(ctx context.Context, repoPath string, revision string) (*git.Commit, error) {
	oid, err := g.getOIDFromRevision(ctx, repoPath, revision+"^{commit}")
	if errors.Is(err, git.ErrRevisionNotFound) {
		return nil, git.ErrCommitNotFound
	}

	content, err := g.runGit(ctx, repoPath, runGitOption{
		Args:           []string{"cat-file", "commit", oid},
		SkipTrimOutput: true,
	})

	return parseCommit(oid, []byte(content))
}

func (g *Git) GetBlob(ctx context.Context, repoPath string, revision string, path string) (*git.Blob, error) {
	oid, err := g.getOIDFromRevision(ctx, repoPath, revision+":"+path)
	if errors.Is(err, git.ErrRevisionNotFound) {
		return nil, git.ErrBlobNotFound
	}
	content, err := g.runGit(ctx, repoPath, runGitOption{
		Args:           []string{"cat-file", "blob", oid},
		SkipTrimOutput: true,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to read content")
	}
	return &git.Blob{
		ID:      oid,
		Content: []byte(content),
	}, nil
}

func (g *Git) CreateCommit(ctx context.Context, repoPath string, opt *git.CreateCommitOpt) (*git.Commit, error) {
	var commit *git.Commit
	if opt.WithIndex {
		args := []string{"commit", "-m", opt.Message, "--allow-empty"}
		_, err := g.runGit(ctx, repoPath, runGitOption{Args: args})
		if err != nil {
			return nil, errors.WithMessage(err, "failed to commit")
		}
		commit, err = g.GetCommit(ctx, repoPath, "HEAD")
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get commit")
		}
		return commit, nil
	}
	worktreeName := fmt.Sprint(time.Now().Nanosecond())
	err := g.withWorkTree(ctx, repoPath, worktreeName, opt.StartCommitID, func(workTreePath string) error {
		err := opt.Func(ctx, workTreePath)
		if err != nil {
			return err
		}
		_, err = g.runGit(ctx, workTreePath, runGitOption{Args: []string{"add", "-A"}})
		if err != nil {
			return errors.WithMessage(err, "failed to add files")
		}
		diffs, err := g.ListDiffFilePaths(ctx, &git.ListDiffFilePathsOpt{
			RepoPath:     workTreePath,
			FromRevision: "HEAD",
			ToIndex:      true,
		})
		if err != nil {
			return errors.WithMessage(err, "failed to list index diffs")
		}
		if len(diffs) != 0 {
			args := []string{"commit", "-m", opt.Message, "--allow-empty"}
			if opt.StartCommitID == git.BlankID {
				args = append(args, "--amend")
			}
			_, err = g.runGit(ctx, workTreePath, runGitOption{Args: args})
			if err != nil {
				return errors.WithMessage(err, "failed to commit")
			}
		}
		commit, err = g.GetCommit(ctx, workTreePath, "HEAD")
		if err != nil {
			return errors.WithMessage(err, "failed to get commit")
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return commit, nil
}

func (g *Git) UpdateRef(ctx context.Context, repoPath string, ref string, commitID string, oldCommitID *string) error {
	args := []string{"update-ref", ref, commitID}
	if oldCommitID != nil {
		args = append(args, *oldCommitID)
	}
	_, err := g.runGit(ctx, repoPath, runGitOption{Args: args})
	return err
}

func (g *Git) GetBranch(ctx context.Context, repoPath string, branchName string) (*git.Branch, error) {
	commit, err := g.GetCommit(ctx, repoPath, git.BranchRefPrefix+branchName)
	if errors.Is(err, git.ErrCommitNotFound) {
		return nil, git.ErrBranchNotFound
	}
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get commit")
	}
	return &git.Branch{
		Name:   branchName,
		Commit: commit,
	}, nil
}

func (g *Git) GetRemote(ctx context.Context, repoPath string, remoteName string) (*git.Remote, error) {
	result := &git.Remote{
		Name: remoteName,
	}
	// get url
	output, err := g.runGit(ctx, repoPath, runGitOption{
		Args: []string{"remote", "get-url", remoteName},
	})
	if ee := new(exec.ExitError); errors.As(err, &ee) && ee.ExitCode() == 2 {
		// remote not found
		return nil, git.ErrRemoteNotFound
	}
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get remote")
	}
	result.URL = output

	// get push url
	output, err = g.runGit(ctx, repoPath, runGitOption{
		Args: []string{"remote", "get-url", "--push", remoteName},
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get remote")
	}
	result.PushURL = output

	return result, nil
}

func (g *Git) ListRemoteNames(ctx context.Context, repoPath string) ([]string, error) {
	stdout, err := g.runGit(ctx, repoPath, runGitOption{
		Args: []string{"remote"},
	})
	if err != nil {
		return nil, err
	}
	if len(stdout) == 0 {
		return nil, nil
	}
	return strings.Split(stdout, "\n"), nil
}

func (g *Git) Push(ctx context.Context, opt *git.PushOpt) error {
	if len(opt.RefSpecs) == 0 {
		return nil
	}
	args := []string{"push", opt.Remote}
	args = append(args, opt.RefSpecs...)
	for _, opt := range opt.PushOptions {
		args = append(args, "--push-option="+opt)
	}
	_, err := g.runGit(ctx, opt.RepoPath, runGitOption{
		Args:   args,
		Stderr: opt.ProgressWriter,
	})
	return err
}

func (g *Git) GetRepoConfig(ctx context.Context, repoPath string, key string) (string, error) {
	args := []string{"config", "--local", "--get", key}
	stdout, err := g.runGit(ctx, repoPath, runGitOption{Args: args})
	if ee := new(exec.ExitError); errors.As(err, &ee) && ee.ExitCode() == 1 {
		return "", git.ErrConfigNotFound
	}
	if err != nil {
		return "", err
	}
	return stdout, nil
}

func (g *Git) UpdateRepoConfig(ctx context.Context, repoPath string, key string, value string) error {
	args := []string{"config", "--local", "--replace-all", key, value}
	_, err := g.runGit(ctx, repoPath, runGitOption{Args: args})
	return err
}

func (g *Git) UnsetRepoConfig(ctx context.Context, repoPath string, key string) error {
	args := []string{"config", "--local", "--unset-all", key}
	_, err := g.runGit(ctx, repoPath, runGitOption{Args: args})
	if ee := new(exec.ExitError); errors.As(err, &ee) && ee.ExitCode() == 5 {
		return nil
	}
	return err
}

func (g *Git) Squash(ctx context.Context, opt *git.SquashOpt) (commitID string, err error) {
	commitID, err = g.CommitTree(ctx, &git.CommitTreeOpt{
		RepoPath:        opt.RepoPath,
		Revision:        opt.End,
		Message:         opt.CommitMessage,
		ParentRevisions: []string{opt.Base},
	})
	if err != nil {
		return "", errors.WithMessagef(err, "failed to commit tree")
	}
	return
}

func (g *Git) ListCommits(ctx context.Context, opt *git.ListCommitsOpt) ([]*git.Commit, error) {
	args := []string{"log", opt.Revision, "--pretty=%H", "-z"}
	if opt.FirstParent {
		args = append(args, "--first-parent")
	}
	if opt.Reverse {
		args = append(args, "--reverse")
	}
	content, err := g.runGit(ctx, opt.RepoPath, runGitOption{
		Args: args,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to call git log")
	}
	output, err := g.runGit(ctx, opt.RepoPath, runGitOption{
		Args:           []string{"cat-file", "--batch", "-Z", "--buffer"},
		Stdin:          strings.NewReader(content),
		SkipTrimOutput: true,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to read commits")
	}
	var commits []*git.Commit
	for {
		idx := strings.Index(output, "\x00")
		if idx < 0 {
			break
		}
		info := output[:idx]
		split := strings.Split(info, " ")
		if len(split) != 3 {
			return nil, errors.Errorf("failed to read commit(%s)", split[0])
		}
		commitID := split[0]
		output = output[idx+1:]
		idx = strings.Index(output, "\x00")
		if idx < 0 {
			return nil, errors.New("wrong format for cat-file output")
		}
		commit, err := parseCommit(commitID, []byte(output[:idx]))
		if err != nil {
			return nil, errors.WithMessage(err, "failed to parse commit")
		}
		commits = append(commits, commit)
		output = output[idx+1:]
	}
	return commits, nil
}

func (g *Git) GetWorktreeStatus(ctx context.Context, repoPath string) (*git.WorktreeStatus, error) {
	output, err := g.runGit(ctx, repoPath, runGitOption{
		Args:           []string{"status", "--porcelain=2", "-z", "--branch"},
		SkipTrimOutput: true,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to call git status")
	}
	status, err := parseStatus(output)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse git status output")
	}
	// check whether is in the middle of a rebase
	gitDir, err := g.GetGitDir(ctx, repoPath)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get git dir")
	}
	_, err = os.Stat(path.Join(gitDir, "rebase-merge"))
	switch {
	case err == nil:
		status.RebaseInProgress = true
	case errors.Is(err, fs.ErrNotExist):
		// not in rebase
	default:
		return nil, errors.WithMessage(err, "failed to stat rebase-merge directory")
	}
	return status, nil
}

type runGitOption struct {
	Args           []string
	Stdin          io.Reader
	Stdout         io.Writer
	Stderr         io.Writer
	Env            []string
	SkipTrimOutput bool
}

func (g *Git) runGit(ctx context.Context, path string, opt runGitOption) (string, error) {
	args := append([]string{"-C", path, "-c", "core.hooksPath=/dev/null"}, opt.Args...)
	cmd := exec.CommandContext(ctx, "git", args...)
	if opt.Stdin != nil {
		cmd.Stdin = opt.Stdin
	}
	if opt.Stdout != nil {
		cmd.Stdout = opt.Stdout
	}
	if opt.Stderr != nil {
		cmd.Stderr = opt.Stderr
	}
	if opt.Env != nil {
		cmd.Env = opt.Env
	}
	if cmd.Stdout != nil || cmd.Stderr != nil {
		err := cmd.Run()
		return "", err
	}
	stdout, err := cmd.Output()
	if err != nil {
		if ee := new(exec.ExitError); errors.As(err, &ee) {
			return "", &exitError{
				stdout:    stdout,
				ExitError: ee,
			}
		}
		return "", err
	}
	if !opt.SkipTrimOutput {
		stdout = bytes.TrimSpace(stdout)
	}
	return string(stdout), nil
}

type exitError struct {
	stdout []byte
	*exec.ExitError
}

func (ee *exitError) Error() string {
	if ee.Stderr != nil {
		return ee.ExitError.Error() + ":" + string(ee.Stderr)
	}
	if ee.stdout != nil {
		return ee.ExitError.Error() + ":" + string(ee.stdout)
	}
	return ee.ExitError.Error()
}

func (ee *exitError) Unwrap() error {
	return ee.ExitError
}
