package impl

import (
	"regexp"

	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/vecode/staircase/util/git"
)

var regexHandlers = []lo.Tuple2[*regexp.Regexp, func(status *git.WorktreeStatus, match []string)]{
	{
		// current commit id
		A: regexp.MustCompile(`^# branch\.oid ([0-9a-f]{40}|\(initial\))\x00`),
		B: func(status *git.WorktreeStatus, match []string) {
			if match[1] != "(initial)" {
				status.CurrentCommitID = &match[1]
			}
		},
	},
	{
		// current branch name
		A: regexp.MustCompile(`^# branch\.head ([^\x00]+)\x00`),
		B: func(status *git.WorktreeStatus, match []string) {
			if match[1] != "(detached)" {
				status.CurrentBranchName = &match[1]
			}
		},
	},
	{
		// other header lines
		A: regexp.MustCompile(`^#[^\x00]*\x00`),
		B: func(status *git.WorktreeStatus, match []string) {
			// ignore
		},
	},
	{
		// tracked ordinary changed entry
		A: regexp.MustCompile(`^1 ([.MTADRCUm?])([.MTADRCUm?]) ([NS])([.CMU])([.CMU])([.CMU]) ([0-7]{6}) ([0-7]{6}) ([0-7]{6}) ([0-9a-f]{40}) ([0-9a-f]{40}) ([^\x00]+)\x00`),
		B: func(status *git.WorktreeStatus, match []string) {
			status.TrackedEntries = append(status.TrackedEntries, &git.TrackedEntry{
				Path:              match[12],
				IndexStatus:       pointer.To(match[1][0]),
				WorkingTreeStatus: pointer.To(match[2][0]),
			})
		},
	},
	{
		// tracked path changed entry
		A: regexp.MustCompile(`^2 ([.MTADRCUm?])([.MTADRCUm?]) ([NS])([.CMU])([.CMU])([.CMU]) ([0-7]{6}) ([0-7]{6}) ([0-7]{6}) ([0-9a-f]{40}) ([0-9a-f]{40}) ([RC])([0-9]+) ([^\x00]+)\x00([^\x00]+)\x00`),
		B: func(status *git.WorktreeStatus, match []string) {
			status.TrackedEntries = append(status.TrackedEntries, &git.TrackedEntry{
				Path:              match[14],
				OriginalPath:      pointer.To(match[15]),
				IndexStatus:       pointer.To(match[1][0]),
				WorkingTreeStatus: pointer.To(match[2][0]),
			})
		},
	},
	{
		// unmerged entry
		A: regexp.MustCompile(`^u ([.MTADRCUm?])([.MTADRCUm?]) ([NS])([.CMU])([.CMU])([.CMU]) ([0-7]{6}) ([0-7]{6}) ([0-7]{6}) ([0-7]{6}) ([0-9a-f]{40}) ([0-9a-f]{40}) ([0-9a-f]{40}) ([^\x00]+)\x00`),
		B: func(status *git.WorktreeStatus, match []string) {
			status.TrackedEntries = append(status.TrackedEntries, &git.TrackedEntry{
				Path:        match[14],
				Unmerged:    true,
				OurStatus:   pointer.To(match[1][0]),
				TheirStatus: pointer.To(match[2][0]),
			})
		},
	},
	{
		// unmerged entry
		A: regexp.MustCompile(`^\? ([^\x00]+)\x00`),
		B: func(status *git.WorktreeStatus, match []string) {
			status.UntrackedPaths = append(status.UntrackedPaths, match[1])
		},
	},
}

// parseWorkingTreeStatus parses the output of "git status --porcelain=2 --branch -z"
func parseStatus(content string) (*git.WorktreeStatus, error) {
	status := &git.WorktreeStatus{}
	for {
		if content == "" {
			break
		}
		var ok bool
		for _, tuple := range regexHandlers {
			match := tuple.A.FindStringSubmatch(content)
			if len(match) == 0 {
				continue
			}
			tuple.B(status, match)
			content = content[len(match[0]):]
			ok = true
			break
		}
		if !ok {
			return nil, errors.New("invalid content")
		}
	}
	return status, nil
}
