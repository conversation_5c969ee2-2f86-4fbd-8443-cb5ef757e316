package impl

import (
	"testing"

	"github.com/AlekSi/pointer"
	"github.com/stretchr/testify/require"

	"code.byted.org/vecode/staircase/util/git"
)

func Test_parseStatus(t *testing.T) {
	type args struct {
		content string
	}
	tests := []struct {
		name string
		args args
		want *git.WorktreeStatus
	}{
		{
			name: "success",
			args: args{
				content: "# branch.oid 49c842e3b177d591478697e6e0635942f8d5039e\x00" +
					"# branch.head master\x00" +
					"# xxx\x00" +
					"1 M. N... 100644 100644 100644 e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 ba2906d0666cf726c7eaadd2cd3db615dedfdf3a main\x00" +
					"2 RD N... 100644 100644 000000 4f7cbe77a37e16a7dce3a6ce6f7a5956a98e3395 fba5c11d6d69672560fd3bcc1d366e7ed92c16d6 R89 y\x00x\x00" +
					"u AA N... 000000 100644 100644 100644 0000000000000000000000000000000000000000 c9c6af7f78bc47490dbf3e822cf2f3c24d4b9061 e6bfff5c1d0f0ecd501552b43a1e13d8008abc31 b\x00" +
					"? .gitignore\x00",
			},
			want: &git.WorktreeStatus{
				CurrentCommitID:   pointer.To("49c842e3b177d591478697e6e0635942f8d5039e"),
				CurrentBranchName: pointer.To("master"),
				TrackedEntries: []*git.TrackedEntry{
					{
						Path:              "main",
						IndexStatus:       pointer.ToByte('M'),
						WorkingTreeStatus: pointer.ToByte('.'),
					},
					{
						Path:              "y",
						OriginalPath:      pointer.To("x"),
						IndexStatus:       pointer.ToByte('R'),
						WorkingTreeStatus: pointer.ToByte('D'),
					},
					{
						Path:        "b",
						Unmerged:    true,
						OurStatus:   pointer.ToByte('A'),
						TheirStatus: pointer.ToByte('A'),
					},
				},
				UntrackedPaths: []string{
					".gitignore",
				},
			},
		},
		{
			name: "success, clean",
			args: args{
				content: "# branch.oid e6e67a4edcb82cdca22721b3889f224526530230\x00" +
					"# branch.head main\x00" +
					"# branch.upstream origin/main\x00" +
					"# branch.ab +0 -0\x00",
			},
			want: &git.WorktreeStatus{
				CurrentCommitID:   pointer.To("e6e67a4edcb82cdca22721b3889f224526530230"),
				CurrentBranchName: pointer.To("main"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseStatus(tt.args.content)
			require.NoError(t, err)
			require.Equal(t, tt.want, got)
		})
	}
}
