package impl

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"code.byted.org/vecode/staircase/util/git"
)

func Test_parseCommit(t *testing.T) {
	type args struct {
		oid     string
		content []byte
	}
	tests := []struct {
		name string
		args args
		want *git.Commit
	}{
		{
			name: "success",
			args: args{
				oid: "bb0965dfb1036c19a25408d00b9874071c19aadf",
				content: []byte(`tree 903e9e6c0f33e28f840807450245c95c14e0f81a
parent a4a57b02e2c1dd4cf21c636203c4a0ae3b65eb15
author author <<EMAIL>> 1703592285 +0800
committer committer <<EMAIL>> 1703645905 +0800

feat: use git to read commit and blob

Change-Id: Ib6e4318e9c1b044eab802a5cb36f4675a4ae525b`),
			},
			want: &git.Commit{
				ID: "bb0965dfb1036c19a25408d00b9874071c19aadf",
				Message: `feat: use git to read commit and blob

Change-Id: Ib6e4318e9c1b044eab802a5cb36f4675a4ae525b`,
				Author: &git.Signature{
					Name:  "author",
					Email: "<EMAIL>",
					When:  time.Unix(1703592285, 0).UTC(),
				},
				Committer: &git.Signature{
					Name:  "committer",
					Email: "<EMAIL>",
					When:  time.Unix(1703645905, 0).UTC(),
				},
				ParentIDs: []string{"a4a57b02e2c1dd4cf21c636203c4a0ae3b65eb15"},
				TreeID:    "903e9e6c0f33e28f840807450245c95c14e0f81a",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseCommit(tt.args.oid, tt.args.content)
			require.NoError(t, err)
			got.Author.When = got.Author.When.UTC()
			got.Committer.When = got.Committer.When.UTC()
			require.Equal(t, tt.want, got)
		})
	}
}
