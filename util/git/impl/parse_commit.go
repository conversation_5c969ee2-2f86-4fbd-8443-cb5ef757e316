package impl

import (
	"bytes"
	"io"

	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/samber/lo"

	"code.byted.org/vecode/staircase/util/git"
)

func parseCommit(oid string, content []byte) (*git.Commit, error) {
	e := &encodedCommit{
		oid:     oid,
		content: content,
	}
	c := new(object.Commit)
	err := c.Decode(e)
	if err != nil {
		return nil, err
	}
	return &git.Commit{
		ID:        c.Hash.String(),
		Message:   c.Message,
		Author:    (*git.Signature)(&c.Author),
		Committer: (*git.Signature)(&c.Committer),
		ParentIDs: lo.Map(c.ParentHashes, func(item plumbing.Hash, _ int) string {
			return item.String()
		}),
		TreeID: c.TreeHash.String(),
	}, nil
}

type encodedCommit struct {
	oid     string
	content []byte
}

func (e *encodedCommit) Hash() plumbing.Hash {
	return plumbing.NewHash(e.oid)
}

func (e *encodedCommit) Type() plumbing.ObjectType {
	return plumbing.CommitObject
}

func (e *encodedCommit) SetType(objectType plumbing.ObjectType) {
	panic("implement me")
}

func (e *encodedCommit) Size() int64 {
	panic("implement me")
}

func (e *encodedCommit) SetSize(i int64) {
	panic("implement me")
}

func (e *encodedCommit) Reader() (io.ReadCloser, error) {
	return io.NopCloser(bytes.NewReader(e.content)), nil
}

func (e *encodedCommit) Writer() (io.WriteCloser, error) {
	panic("implement me")
}
