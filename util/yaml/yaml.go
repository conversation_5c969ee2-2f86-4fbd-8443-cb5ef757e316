package yaml

import (
	"gopkg.in/yaml.v3"
)

type StringLiteral string

func (s StringLiteral) MarshalYAML() (interface{}, error) {
	return yaml.Node{
		Kind:  yaml.ScalarNode,
		Style: yaml.LiteralStyle,
		Value: string(s),
	}, nil
}

type SingleQuotedString string

func (s SingleQuotedString) MarshalYAML() (interface{}, error) {
	return yaml.Node{
		Kind:  yaml.ScalarNode,
		Style: yaml.SingleQuotedStyle,
		Value: string(s),
	}, nil
}

type StringWithComment struct {
	Value   string
	Comment string
}

func (s *StringWithComment) UnmarshalYAML(node *yaml.Node) error {
	return node.Decode(&s.Value)
}

func (s *StringWithComment) MarshalYAML() (interface{}, error) {
	return yaml.Node{
		Kind:        yaml.ScalarNode,
		Value:       s.Value,
		LineComment: s.Comment,
	}, nil
}
