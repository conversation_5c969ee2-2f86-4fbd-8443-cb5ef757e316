package yaml

import (
	"testing"

	"github.com/stretchr/testify/require"
	"gopkg.in/yaml.v3"
)

func Test(t *testing.T) {
	type T struct {
		S  StringLiteral      `yaml:"s,omitempty"`
		Q  SingleQuotedString `yaml:"q,omitempty"`
		SC *StringWithComment `yaml:"sc,omitempty"`
	}

	raw, err := yaml.<PERSON>(T{
		S: "single line",
		Q: "a'bc",
		SC: &StringWithComment{
			Value: "abc",
		},
	})
	require.NoError(t, err)
	require.Equal(t, `s: |-
    single line
q: 'a''bc'
sc: abc
`, string(raw))

	raw, err = yaml.Marshal(T{
		S: "multi line\nmulti line\n",
		Q: "abc",
		SC: &StringWithComment{
			Value:   "abc",
			Comment: "comment",
		},
	})
	require.NoError(t, err)
	require.Equal(t, `s: |
    multi line
    multi line
q: 'abc'
sc: abc # comment
`, string(raw))

	tt := T{}
	err = yaml.Unmarshal([]byte(`s: |-
    single line
q: 'a''bc'
sc: abc
`), &tt)
	require.NoError(t, err)
	require.Equal(t, T{
		S: "single line",
		Q: "a'bc",
		SC: &StringWithComment{
			Value:   "abc",
			Comment: "",
		},
	}, tt)
}
