package impl

import (
	"context"
	"os/exec"

	"github.com/pkg/errors"

	"code.byted.org/vecode/staircase/util/gitabsorb"
)

type Util struct{}

func (u *Util) Do(ctx context.Context, opt *gitabsorb.Opt) error {
	args := []string{"--base", opt.BaseRevision}
	if opt.Force {
		args = append(args, "--force")
	}
	cmd := exec.CommandContext(ctx, "git-absorb", args...)
	cmd.Dir = opt.RepoPath

	stdout, err := cmd.Output()
	if err != nil {
		if ee := new(exec.ExitError); errors.As(err, &ee) {
			return &exitError{
				stdout:    stdout,
				ExitError: ee,
			}
		}
		return err
	}
	return nil
}

type exitError struct {
	stdout []byte
	*exec.ExitError
}

func (ee *exitError) Error() string {
	if ee.Stderr != nil {
		return ee.ExitError.Error() + ":" + string(ee.Stderr)
	}
	if ee.stdout != nil {
		return ee.ExitError.Error() + ":" + string(ee.stdout)
	}
	return ee.ExitError.Error()
}

func (ee *exitError) Unwrap() error {
	return ee.ExitError
}
