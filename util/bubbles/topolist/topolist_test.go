package topolist

import (
	"strings"
	"testing"

	"github.com/AlekSi/pointer"
	"github.com/stretchr/testify/require"
)

type item struct {
	key       string
	parentKey *string
}

func (t item) Key() string {
	return t.key
}

func (t item) ParentKey() *string {
	return t.parentKey
}

type itemDelegate struct{}

func (d itemDelegate) Render(_ Model, _ int, t Item) string {
	return t.(item).key
}

func Test_calcItemColumns(t *testing.T) {
	type args struct {
		items []Item
	}
	tests := []struct {
		name        string
		args        args
		wantColumns []int
		wantWidth   int
	}{
		{
			name: "normal",
			args: args{
				items: []Item{
					item{key: "y", parentKey: pointer.To("x")},
					item{key: "g", parentKey: pointer.To("e")},
					item{key: "f", parentKey: pointer.To("a")},
					item{key: "e", parentKey: pointer.To("c")},
					item{key: "d", parentKey: pointer.To("b")},
					item{key: "c", parentKey: pointer.To("b")},
					item{key: "b", parentKey: pointer.To("a")},
					item{key: "a", parentKey: nil},
					item{key: "x", parentKey: nil},
				},
			},
			wantColumns: []int{1, 2, 4, 2, 3, 2, 2, 2, 1},
			wantWidth:   4,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotColumns, gotWidth := calcItemColumns(tt.args.items)
			require.Equal(t, tt.wantColumns, gotColumns)
			require.Equal(t, tt.wantWidth, gotWidth)
		})
	}
}

func Test_renderDashLineGraph(t *testing.T) {
	type args struct {
		items          []Item
		columns        []int
		contentHeights []int
	}
	tests := []struct {
		name      string
		args      args
		want      string
		wantLines []int
	}{
		{
			/*
				z
				|   j
				| k |
				| |/  x
				| m   |
				| |  /    e
				| | /   d |
				| |/    |/
				| g     |
				| |    /
				| c   /
				| |  /
				| | /
				| |/
				| b
				y
			*/
			name: "normal",
			args: args{
				items: []Item{
					item{key: "z", parentKey: pointer.To("y")},
					item{key: "j", parentKey: pointer.To("m")},
					item{key: "k", parentKey: pointer.To("m")},
					item{key: "x", parentKey: pointer.To("g")},
					item{key: "m", parentKey: pointer.To("g")},
					item{key: "e", parentKey: pointer.To("b")},
					item{key: "d", parentKey: pointer.To("b")},
					item{key: "g", parentKey: pointer.To("c")},
					item{key: "c", parentKey: pointer.To("b")},
					item{key: "b", parentKey: nil},
					item{key: "y", parentKey: nil},
				},
				columns:        []int{1, 3, 2, 4, 2, 6, 5, 2, 2, 2, 1},
				contentHeights: []int{1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1},
			},
			want: strings.Join([]string{
				"*          \n",
				"|   *      \n",
				"| * |      \n",
				"| |/  *    \n",
				"| *   |    \n",
				"| |  /    *\n",
				"| | /   * |\n",
				"| |/    |/ \n",
				"| *     |  \n",
				"| |    /   \n",
				"| *   /    \n",
				"| |  /     \n",
				"| | /      \n",
				"| |/       \n",
				"| *        \n",
				"*          \n",
			}, ""),
			wantLines: []int{1, 2, 3, 4, 5, 6, 7, 9, 11, 15, 16},
		},
		{
			/*
				  e
				d |
				| |
				c |
				| |
				b |
				|/
				a

			*/
			name: "normal",
			args: args{
				items: []Item{
					item{key: "e", parentKey: pointer.To("a")},
					item{key: "d", parentKey: pointer.To("c")},
					item{key: "c", parentKey: pointer.To("b")},
					item{key: "b", parentKey: pointer.To("a")},
					item{key: "a", parentKey: nil},
				},
				columns:        []int{2, 1, 1, 1, 1},
				contentHeights: []int{1, 1, 1, 1, 1},
			},
			want: strings.Join([]string{
				"  *\n",
				"* |\n",
				"| |\n",
				"* |\n",
				"| |\n",
				"* |\n",
				"|/ \n",
				"*  \n",
			}, ""),
			wantLines: []int{1, 2, 4, 6, 8},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotLines := renderDashLineGraph(tt.args.items, tt.args.columns, tt.args.contentHeights)
			t.Log("want\n", tt.want)
			t.Log("got\n", got)
			require.Equal(t, tt.want, got)
			require.Equal(t, tt.wantLines, gotLines)
		})
	}
}

func Test_combineGraphAndContents(t *testing.T) {
	type args struct {
		graph    string
		lines    []int
		contents []string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "normal",
			args: args{
				graph: strings.Join([]string{
					"*          \n",
					"|   *      \n",
					"| * |      \n",
					"| |/  *    \n",
					"| *   |    \n",
					"| |  /    *\n",
					"| | /   * |\n",
					"| |/    |/ \n",
					"| *     |  \n",
					"| |    /   \n",
					"| *   /    \n",
					"| |  /     \n",
					"| | /      \n",
					"| |/       \n",
					"| *        \n",
					"*          \n",
				}, ""),
				lines:    []int{1, 2, 3, 4, 5, 6, 7, 9, 11, 15, 16},
				contents: []string{"z", "j", "k", "x", "m", "e", "d", "g", "c", "b", "y"},
			},
			want: strings.Join([]string{
				"*           z\n",
				"|   *       j\n",
				"| * |       k\n",
				"| |/  *     x\n",
				"| *   |     m\n",
				"| |  /    * e\n",
				"| | /   * | d\n",
				"| |/    |/ \n",
				"| *     |   g\n",
				"| |    /   \n",
				"| *   /     c\n",
				"| |  /     \n",
				"| | /      \n",
				"| |/       \n",
				"| *         b\n",
				"*           y\n",
			}, ""),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := combineGraphAndContents(tt.args.graph, tt.args.lines, tt.args.contents)
			t.Log("want\n", tt.want)
			t.Log("got\n", got)
			require.Equal(t, tt.want, got)
		})
	}
}

func TestModel_View(t *testing.T) {
	type fields struct {
		delegate  ItemDelegate
		items     []Item
		solidLine bool
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "dash line",
			fields: fields{
				delegate: itemDelegate{},
				items: []Item{
					item{key: "z", parentKey: pointer.To("y")},
					item{key: "j", parentKey: pointer.To("m")},
					item{key: "k", parentKey: pointer.To("m")},
					item{key: "x", parentKey: pointer.To("g")},
					item{key: "m", parentKey: pointer.To("g")},
					item{key: "e", parentKey: pointer.To("b")},
					item{key: "d", parentKey: pointer.To("b")},
					item{key: "g", parentKey: pointer.To("c")},
					item{key: "c", parentKey: pointer.To("b")},
					item{key: "b", parentKey: nil},
					item{key: "y", parentKey: nil},
				},
			},
			want: strings.Join([]string{
				"*           z\n",
				"|   *       j\n",
				"| * |       k\n",
				"| |/  *     x\n",
				"| *   |     m\n",
				"| |  /    * e\n",
				"| | /   * | d\n",
				"| |/    |/ \n",
				"| *     |   g\n",
				"| |    /   \n",
				"| *   /     c\n",
				"| |  /     \n",
				"| | /      \n",
				"| |/       \n",
				"| *         b\n",
				"*           y\n",
			}, ""),
		},
		{
			name: "solid line",
			fields: fields{
				delegate: itemDelegate{},
				items: []Item{
					item{key: "z", parentKey: pointer.To("y")},
					item{key: "j", parentKey: pointer.To("m")},
					item{key: "k", parentKey: pointer.To("m")},
					item{key: "x", parentKey: pointer.To("g")},
					item{key: "m", parentKey: pointer.To("g")},
					item{key: "e", parentKey: pointer.To("b")},
					item{key: "d", parentKey: pointer.To("b")},
					item{key: "g", parentKey: pointer.To("c")},
					item{key: "c", parentKey: pointer.To("b")},
					item{key: "b", parentKey: nil},
					item{key: "y", parentKey: nil},
				},
				solidLine: true,
			},
			want: strings.Join([]string{
				"◯           z\n",
				"│   ◯       j\n",
				"│ ◯ │       k\n",
				"│ │ │ ◯     x\n",
				"│ ◯─┘ │     m\n",
				"│ │   │   ◯ e\n",
				"│ │   │ ◯ │ d\n",
				"│ ◯───┘ │ │ g\n",
				"│ │     │ │\n",
				"│ ◯     │ │ c\n",
				"│ │     │ │\n",
				"│ ◯─────┴─┘ b\n",
				"◯           y\n",
			}, ""),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := Model{
				ItemDelegate:   tt.fields.delegate,
				Items:          tt.fields.items,
				SolidLineGraph: tt.fields.solidLine,
			}
			got := m.View()
			t.Log("want\n", tt.want)
			t.Log("got\n", got)
			require.Equal(t, tt.want, got)
		})
	}
}

func Test_renderSolidLineGraph(t *testing.T) {
	type args struct {
		items          []Item
		columns        []int
		contentHeights []int
	}
	tests := []struct {
		name      string
		args      args
		want      string
		wantLines []int
	}{
		{
			/*
				◯--------------z
				│   ◯----------j
				│ ◯-│----------k
				│ │ │ ◯--------x
				│ ◯─┘-│--------m
				│ │   │   ◯----e
				│ │   │ ◯-│----d
				│ ◯───┘-│-│----g
				│ │     │ │
				│ ◯-----│-│----c
				│ │     │ │
				│ ◯─────┴─┘----b
				◯--------------y
			*/
			name: "normal",
			args: args{
				items: []Item{
					item{key: "z", parentKey: pointer.To("y")},
					item{key: "j", parentKey: pointer.To("m")},
					item{key: "k", parentKey: pointer.To("m")},
					item{key: "x", parentKey: pointer.To("g")},
					item{key: "m", parentKey: pointer.To("g")},
					item{key: "e", parentKey: pointer.To("b")},
					item{key: "d", parentKey: pointer.To("b")},
					item{key: "g", parentKey: pointer.To("c")},
					item{key: "c", parentKey: pointer.To("b")},
					item{key: "b", parentKey: nil},
					item{key: "y", parentKey: nil},
				},
				columns:        []int{1, 3, 2, 4, 2, 6, 5, 2, 2, 2, 1},
				contentHeights: []int{1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1},
			},
			want: strings.Join([]string{
				"◯          \n",
				"│   ◯      \n",
				"│ ◯ │      \n",
				"│ │ │ ◯    \n",
				"│ ◯─┘ │    \n",
				"│ │   │   ◯\n",
				"│ │   │ ◯ │\n",
				"│ ◯───┘ │ │\n",
				"│ │     │ │\n",
				"│ ◯     │ │\n",
				"│ │     │ │\n",
				"│ ◯─────┴─┘\n",
				"◯          \n",
			}, ""),
			wantLines: []int{1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 13},
		},
		{
			/*
				  e
				d │
				│ │
				c │
				│ │
				b │
				│ │
				a─┘

			*/
			name: "normal",
			args: args{
				items: []Item{
					item{key: "e", parentKey: pointer.To("a")},
					item{key: "d", parentKey: pointer.To("c")},
					item{key: "c", parentKey: pointer.To("b")},
					item{key: "b", parentKey: pointer.To("a")},
					item{key: "a", parentKey: nil},
				},
				columns:        []int{2, 1, 1, 1, 1},
				contentHeights: []int{1, 1, 1, 1, 1},
			},
			want: strings.Join([]string{
				"  ◯\n",
				"◯ │\n",
				"│ │\n",
				"◯ │\n",
				"│ │\n",
				"◯ │\n",
				"│ │\n",
				"◯─┘\n",
			}, ""),
			wantLines: []int{1, 2, 4, 6, 8},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotLines := renderSolidLineGraph(tt.args.items, tt.args.columns, tt.args.contentHeights)
			t.Log("want\n", tt.want)
			t.Log("got\n", got)
			require.Equal(t, tt.want, got)
			require.Equal(t, tt.wantLines, gotLines)
		})
	}
}
