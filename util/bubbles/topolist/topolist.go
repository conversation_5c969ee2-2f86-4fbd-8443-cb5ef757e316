package topolist

import (
	"bytes"
	"strings"

	"github.com/AlekSi/pointer"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/samber/lo"
)

type ItemDelegate interface {
	// Render renders the item's view.
	Render(m Model, index int, item Item) string
}

type Item interface {
	Key() string
	ParentKey() *string
}

type Model struct {
	ItemDelegate ItemDelegate
	// Items must be in reverse-topological order where parents appear after children
	Items        []Item
	EnableChoice bool
	// Index is the index of selected item, starts from 0
	Index            int
	SolidLineGraph   bool
	EnablePagination bool
	Height           int

	topIndex    *int
	botIndex    *int
	initialized bool
}

func (m *Model) View() string {
	if !m.initialized {
		m.topIndex = pointer.To(0)
		m.initialized = true
	}
	if m == nil || len(m.Items) == 0 || (m.EnablePagination && m.Height == 0) {
		return ""
	}
	contents := make([]string, len(m.Items))
	for i, item := range m.Items {
		contents[i] = m.ItemDelegate.Render(*m, i, item)
	}
	columns, _ := calcItemColumns(m.Items)
	heights := make([]int, len(m.Items))
	for i, content := range contents {
		heights[i] = strings.Count(content, "\n") + 1
	}
	var (
		graph string
		lines []int
	)
	if m.SolidLineGraph {
		graph, lines = renderSolidLineGraph(m.Items, columns, heights, m.EnableChoice, m.Index)
	} else {
		graph, lines = renderDashLineGraph(m.Items, columns, heights)
	}
	fullContent := combineGraphAndContents(graph, lines, contents)
	if !m.EnablePagination {
		return fullContent
	}
	contentLines := strings.Split(fullContent, "\n")
	lines = append(lines, len(contentLines)+1)
	if m.topIndex != nil {
		if m.Index < *m.topIndex {
			// set top index
			*m.topIndex = m.Index
		} else if m.Index > *m.topIndex {
			if m.Height >= lines[m.Index+1]-lines[*m.topIndex] {
				// can display from topIndex to Index, do nothing
			} else if m.Height >= lines[m.Index+1]-lines[m.Index] {
				// can display from botIndex, clear top index, set bot index
				m.topIndex = nil
				m.botIndex = pointer.To(m.Index)
			} else {
				// can display from topIndex, set top index, clear bot index
				*m.topIndex = m.Index
			}
		}
	} else if m.botIndex != nil {
		if m.Index < *m.botIndex {
			if m.Height >= lines[*m.botIndex+1]-lines[m.Index] {
				// can display from botIndex to Index, do nothing
			} else {
				// can display from topIndex, set top index, clear bot index
				m.topIndex = pointer.To(m.Index)
				m.botIndex = nil
			}
		} else {
			if m.Height >= lines[m.Index+1]-lines[m.Index] {
				// can display Index, set bot index
				*m.botIndex = m.Index
			} else {
				// can display from topIndex, set top index, clear bot index
				m.topIndex = pointer.To(m.Index)
				m.botIndex = nil
			}
		}
	} else {
		// set top index
		m.topIndex = pointer.To(m.Index)
	}

	if m.topIndex != nil {
		return strings.Join(contentLines[lines[*m.topIndex]-1:min(lines[*m.topIndex]-1+m.Height, len(contentLines))], "\n")
	}
	return strings.Join(contentLines[max(lines[*m.botIndex+1]-1-m.Height, 0):lines[*m.botIndex+1]-1], "\n")
}

func (m *Model) Init() tea.Cmd {
	return nil
}

func (m *Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "down", "j":
			if m.Index >= len(m.Items)-1 {
				m.Index = 0
			} else {
				m.Index++
			}
		case "up", "k":
			if m.Index <= 0 {
				m.Index = max(len(m.Items)-1, 0)
			} else {
				m.Index--
			}
		}
	case tea.WindowSizeMsg:
		m.Height = msg.Height
	}

	return m, nil
}

func calcItemColumns(items []Item) ([]int, int) {
	visited := make([]bool, len(items))
	result := make([]int, len(items))
	column := 1
	for i := len(items) - 1; i >= 0; i-- {
		if visited[i] {
			continue
		}
		column += calcItemColumnsInTree(items, column, i, visited, result)
	}
	return result, column - 1
}

func calcItemColumnsInTree(items []Item, currentColumn, currentIdx int, visited []bool, result []int) (width int) {
	visited[currentIdx] = true
	result[currentIdx] = currentColumn

	t := items[currentIdx]
	var currentWidth int
	for i := currentIdx - 1; i >= 0; i-- {
		if items[i].ParentKey() == nil || *items[i].ParentKey() != t.Key() {
			continue
		}
		currentWidth += calcItemColumnsInTree(items, currentColumn+currentWidth, i, visited, result)
	}
	if currentWidth == 0 {
		currentWidth = 1
	}
	return currentWidth
}

func renderDashLineGraph(items []Item, columns []int, contentHeights []int) (string, []int) {
	maxColumn := lo.Max(columns)

	lines := make([]int, len(items))
	lines[0] = 1
	for i := 1; i < len(items); i++ {
		lines[i] = lines[i-1] + 1
		if lines[i] < lines[i-1]+contentHeights[i-1] {
			lines[i] = lines[i-1] + contentHeights[i-1]
		}
		for j := 0; j < i; j++ {
			if items[j].ParentKey() == nil {
				continue
			}
			if *items[j].ParentKey() != items[i].Key() {
				continue
			}
			if columns[j] == columns[i] && lines[i] < lines[j]+2 {
				lines[i] = lines[j] + 2
				continue
			}
			if lines[i] < lines[j]+(columns[j]-columns[i])*2+1 {
				lines[i] = lines[j] + (columns[j]-columns[i])*2 + 1
			}
		}
	}
	idxsByKey := make(map[string]int)
	for i, item := range items {
		idxsByKey[item.Key()] = i
	}
	totalHeight := lines[len(items)-1] + contentHeights[len(items)-1] - 1
	result := bytes.Repeat([]byte{' '}, totalHeight*(maxColumn*2))
	var activeItems []Item
	for i, item := range items {
		{
			// remove non-active items
			i := 0
			for i < len(activeItems) {
				if activeItems[i].ParentKey() != nil && *activeItems[i].ParentKey() == item.Key() {
					activeItems = append(activeItems[:i], activeItems[i+1:]...)
				} else {
					i++
				}
			}
		}

		activeItems = append(activeItems, item)

		var realHeight int
		if i == len(items)-1 {
			realHeight = contentHeights[i]
			if realHeight < 1 {
				realHeight = 1
			}
		} else {
			realHeight = lines[i+1] - lines[i]
		}

		for l := lines[i]; l < lines[i]+realHeight; l++ {
			for _, activeItem := range activeItems {
				line := lines[idxsByKey[activeItem.Key()]]
				column := columns[idxsByKey[activeItem.Key()]]
				if l == line {
					result[(l-1)*(maxColumn*2)+(column-1)*2] = '*'
					continue
				}
				parentItemKey := activeItem.ParentKey()
				if parentItemKey == nil {
					continue
				}
				parentLine := lines[idxsByKey[*parentItemKey]]
				parentColumn := columns[idxsByKey[*parentItemKey]]
				if l <= parentLine-(column-parentColumn)*2 {
					result[(l-1)*(maxColumn*2)+(column-1)*2] = '|'
					continue
				}
				result[(l-1)*(maxColumn*2)+(parentColumn-1)*2+(parentLine-l)] = '/'
			}
			result[l*(maxColumn*2)-1] = '\n'
		}

		if item.ParentKey() == nil {
			activeItems = activeItems[:len(activeItems)-1]
		}
	}
	return string(result), lines
}

func renderSolidLineGraph(items []Item, columns []int, contentHeights []int, enableChoice bool, index int) (string, []int) {
	maxColumn := lo.Max(columns)

	lines := make([]int, len(items))
	lines[0] = 1
	for i := 1; i < len(items); i++ {
		lines[i] = lines[i-1] + 1
		if lines[i] < lines[i-1]+contentHeights[i-1] {
			lines[i] = lines[i-1] + contentHeights[i-1]
		}
		for j := 0; j < i; j++ {
			if items[j].ParentKey() == nil {
				continue
			}
			if *items[j].ParentKey() != items[i].Key() {
				continue
			}
			if lines[i] < lines[j]+2 {
				lines[i] = lines[j] + 2
				continue
			}
		}
	}
	idxsByKey := make(map[string]int)
	for i, item := range items {
		idxsByKey[item.Key()] = i
	}
	totalHeight := lines[len(items)-1] + contentHeights[len(items)-1] - 1
	result := make([]rune, totalHeight*(maxColumn*2))
	for i := range result {
		result[i] = ' '
	}

	var activeItems []Item
	for i, item := range items {
		activeItems = append(activeItems, item)

		var realHeight int
		if i == len(items)-1 {
			realHeight = contentHeights[i]
			if realHeight < 1 {
				realHeight = 1
			}
		} else {
			realHeight = lines[i+1] - lines[i]
		}

		for l := lines[i]; l < lines[i]+realHeight; l++ {
			for _, activeItem := range activeItems {
				line := lines[idxsByKey[activeItem.Key()]]
				column := columns[idxsByKey[activeItem.Key()]]
				if l == line {
					result[(l-1)*(maxColumn*2)+(column-1)*2] = lo.Ternary(enableChoice && i == index, '◉', '◯')
					continue
				}
				parentItemKey := activeItem.ParentKey()
				if parentItemKey == nil {
					continue
				}
				parentLine := lines[idxsByKey[*parentItemKey]]
				parentColumn := columns[idxsByKey[*parentItemKey]]
				if l < parentLine {
					result[(l-1)*(maxColumn*2)+(column-1)*2] = '│'
					continue
				}
				if l > parentLine {
					continue
				}
				for c := (parentColumn-1)*2 + 1; c < (column-1)*2; c++ {
					offset := (l-1)*(maxColumn*2) + c
					switch result[offset] {
					case ' ':
						result[offset] = '─'
					case '┘':
						result[offset] = '┴'
					default:
						continue
					}
				}
				{
					// joint point
					offset := (l-1)*(maxColumn*2) + (column-1)*2
					switch result[offset] {
					case ' ':
						result[offset] = '┘'
					case '─':
						result[offset] = '┴'
					default:
						continue
					}
				}
			}
			result[l*(maxColumn*2)-1] = '\n'
		}

		{
			// remove non-active items
			i := 0
			for i < len(activeItems) {
				if activeItems[i].ParentKey() != nil && *activeItems[i].ParentKey() == item.Key() {
					activeItems = append(activeItems[:i], activeItems[i+1:]...)
				} else {
					i++
				}
			}
		}

		if item.ParentKey() == nil {
			activeItems = activeItems[:len(activeItems)-1]
		}
	}
	return string(result), lines
}

func combineGraphAndContents(graph string, lines []int, contents []string) string {
	graphLines := strings.Split(graph, "\n")
	graphLines = graphLines[:len(graphLines)-1]
	for i, line := range lines {
		contentLines := strings.Split(contents[i], "\n")
		for j, contentLine := range contentLines {
			graphLines[line-1+j] += " "
			graphLines[line-1+j] += contentLine
		}
	}
	return strings.Join(graphLines, "\n") + "\n"
}
