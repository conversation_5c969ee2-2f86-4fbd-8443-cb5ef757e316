package priorityqueue

import (
	"container/heap"
)

type item struct {
	node  Node
	index int
}

type itemHeap []*item

func (h *itemHeap) Len() int {
	return len(*h)
}

func (h *itemHeap) Less(i, j int) bool {
	return (*h)[i].node.Less((*h)[j].node)
}

func (h *itemHeap) Swap(i, j int) {
	(*h)[i], (*h)[j] = (*h)[j], (*h)[i]
	(*h)[i].index = i
	(*h)[j].index = j
}

func (h *itemHeap) Push(x any) {
	*h = append(*h, x.(*item))
}

func (h *itemHeap) Pop() any {
	old := *h
	n := len(old)
	x := old[n-1]
	*h = old[0 : n-1]
	return x
}

type PriorityQueue struct {
	h     itemHeap
	items map[string]*item
}

func (pq *PriorityQueue) Push(node Node) {
	if pq.items == nil {
		pq.items = make(map[string]*item)
	}
	_, ok := pq.items[node.Key()]
	if ok {
		pq.Update(node)
		return
	}
	newItem := &item{
		node:  node,
		index: len(pq.h),
	}
	pq.items[node.Key()] = newItem
	heap.Push(&pq.h, newItem)
}

func (pq *PriorityQueue) Pop() (node Node, ok bool) {
	if len(pq.items) == 0 {
		return nil, false
	}
	poppedItem := heap.Pop(&pq.h)
	if poppedItem == nil {
		return nil, false
	}
	node = poppedItem.(*item).node
	delete(pq.items, node.Key())
	return node, true
}

func (pq *PriorityQueue) Len() int {
	return pq.h.Len()
}

func (pq *PriorityQueue) Update(node Node) {
	old, ok := pq.items[node.Key()]
	if !ok {
		pq.Push(node)
		return
	}
	old.node = node
	heap.Fix(&pq.h, old.index)
}

type Node interface {
	Key() string
	Less(other Node) bool
}
