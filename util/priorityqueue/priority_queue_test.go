package priorityqueue

import (
	"testing"

	"github.com/stretchr/testify/require"
)

type node struct {
	key   string
	value int
}

func (n node) Key() string {
	return n.key
}

func (n node) Less(other Node) bool {
	return n.value < other.(node).value
}

func TestPriorityQueue(t *testing.T) {
	// pop from empty queue
	pq := new(PriorityQueue)
	_, ok := pq.Pop()
	require.False(t, ok)

	// push nodes with same key twice
	pq = new(PriorityQueue)
	pq.Push(node{"1", 1})
	pq.Push(node{"1", 2})
	n, ok := pq.Pop()
	require.True(t, ok)
	require.Equal(t, node{"1", 2}, n)
	_, ok = pq.Pop()
	require.False(t, ok)

	// update a not existing node
	pq = new(PriorityQueue)
	pq.Update(node{"1", 1})
	n, ok = pq.Pop()
	require.True(t, ok)
	require.Equal(t, node{"1", 1}, n)
	_, ok = pq.Pop()
	require.False(t, ok)

	// update
	pq = new(PriorityQueue)
	pq.Push(node{"3", 3})
	pq.Push(node{"1", 1})
	pq.Push(node{"2", 2})
	n, ok = pq.Pop()
	require.True(t, ok)
	require.Equal(t, node{"1", 1}, n)
	pq.Update(node{"2", 4})
	n, ok = pq.Pop()
	require.True(t, ok)
	require.Equal(t, node{"3", 3}, n)
	n, ok = pq.Pop()
	require.True(t, ok)
	require.Equal(t, node{"2", 4}, n)
	_, ok = pq.Pop()
	require.False(t, ok)
}
