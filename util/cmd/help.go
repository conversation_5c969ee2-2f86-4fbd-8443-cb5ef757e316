package cmd

import (
	"github.com/charmbracelet/lipgloss"
	"github.com/spf13/cobra"
)

func AddAdditionalHelp(cmd *cobra.Command, help string) {
	root := cmd.Root()
	if _, ok := root.Annotations["HelpTemplateModifiedForAdditionalHelp"]; !ok {
		root.SetHelpTemplate(root.HelpTemplate() + `{{with (index .Annotations "AdditionalHelp")}}{{if .}}
Additional help:
{{. | trimTrailingWhitespaces}}
{{end}}{{end}}`)
		if root.Annotations == nil {
			root.Annotations = make(map[string]string)
		}
		root.Annotations["HelpTemplateModifiedForAdditionalHelp"] = ""
	}
	if cmd.Annotations == nil {
		cmd.Annotations = make(map[string]string)
	}
	cmd.Annotations["AdditionalHelp"] = help
}

func GetAdditionalHelp(cmd *cobra.Command) string {
	return cmd.Annotations["AdditionalHelp"]
}

func RenderHelp(s string) string {
	return lipgloss.NewStyle().PaddingLeft(2).Width(70).Render(s)
}
