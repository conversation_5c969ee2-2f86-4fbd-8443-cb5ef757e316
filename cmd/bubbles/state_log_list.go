package bubbles

import (
	"io"

	"github.com/charmbracelet/bubbles/list"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/samber/lo"

	"code.byted.org/vecode/staircase/util/git"
)

func NewStateLogList(items []*git.Commit) StateLogList {
	l := list.New(
		lo.Map(items, func(item *git.Commit, _ int) list.Item {
			return &StateLogListItem{
				Commit: item,
			}
		}),
		StateLogListItemDelegate{},
		0,
		0,
	)
	l.SetShowPagination(false)
	l.SetShowTitle(false)
	l.SetShowStatusBar(false)
	l.SetFilteringEnabled(false)
	return StateLogList{
		list: l,
	}
}

type StateLogList struct {
	list      list.Model
	quitting  bool
	confirmed bool
}

func (m StateLogList) Init() tea.Cmd {
	return nil
}

func (m StateLogList) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q", "esc":
			m.quitting = true
			return m, tea.Quit
		case "enter":
			if len(m.list.Items()) == 0 {
				return m, nil
			}
			m.confirmed = true
			m.quitting = true
			return m, tea.Quit
		}
	case tea.WindowSizeMsg:
		m.list.SetSize(msg.Width, msg.Height)
		return m, nil
	}
	lm, cmd := m.list.Update(msg)
	m.list = lm
	return m, cmd
}

func (m StateLogList) View() string {
	if m.quitting {
		return ""
	}
	return m.list.View()
}

func (m StateLogList) ConfirmedIndex() int {
	if m.confirmed {
		return m.list.Index()
	}
	return -1
}

type StateLogListItem struct {
	Commit *git.Commit
}

func (t *StateLogListItem) FilterValue() string {
	return t.Commit.Subject()
}

type StateLogListItemDelegate struct{}

func (d StateLogListItemDelegate) Render(w io.Writer, m list.Model, index int, item list.Item) {
	t := item.(*StateLogListItem)
	cursorStyle := lipgloss.NewStyle().Foreground(lipgloss.ANSIColor(3))
	timeStyle := lipgloss.NewStyle().Foreground(lipgloss.ANSIColor(2))
	infoStyle := lipgloss.NewStyle().Foreground(lipgloss.ANSIColor(3))
	rawTime := t.Commit.Committer.When.Format("Mon Jan 1 15:04:05 2006 -0700")
	info := t.Commit.Subject()
	var content string
	if m.Index() == index {
		content = lipgloss.JoinHorizontal(lipgloss.Top,
			cursorStyle.Render("▶"),
			"undo to ",
			timeStyle.Render(rawTime),
			", before ",
			infoStyle.Render(info),
		)
	} else {
		content = lipgloss.JoinHorizontal(lipgloss.Top,
			" undo to ",
			timeStyle.Render(rawTime),
			", before ",
			infoStyle.Render(info),
		)
	}
	_, _ = w.Write([]byte(content))
}

func (d StateLogListItemDelegate) Height() int {
	return 1
}

func (d StateLogListItemDelegate) Spacing() int {
	return 0
}

func (d StateLogListItemDelegate) Update(msg tea.Msg, m *list.Model) tea.Cmd {
	return nil
}
