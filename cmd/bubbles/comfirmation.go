package bubbles

import (
	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type Confirmation struct {
	Title         string
	DefaultChoice bool

	ti      textinput.Model
	aborted bool
}

func (c *Confirmation) Init() tea.Cmd {
	ti := textinput.New()
	ti.Placeholder = lo.Ternary(c.DefaultChoice, "y", "n")
	ti.CharLimit = 1
	ti.Validate = func(s string) error {
		if s != "y" && s != "n" {
			return errors.New("input must be y or n")
		}
		return nil
	}
	cmd := ti.Focus()
	c.ti = ti
	return cmd
}

func (c *Confirmation) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd
	c.ti, cmd = c.ti.Update(msg)
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q", "esc":
			c.aborted = true
			return c, tea.Quit
		case "enter", "y", "n":
			return c, tea.Quit
		}
	}
	return c, cmd
}

func (c *Confirmation) View() string {
	return lipgloss.JoinVertical(lipgloss.Left, c.Title, c.ti.View(), "(y to confirm, n to deny, q or esc to quit)")
}

func (c *Confirmation) Confirmed() bool {
	return !c.aborted && ((c.ti.Value() != "" && c.ti.Value() == "y") || (c.ti.Value() == "" && c.DefaultChoice))
}
