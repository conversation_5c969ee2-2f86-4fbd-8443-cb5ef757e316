package bubbles

import (
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/samber/lo"

	changemodel "code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/util/bubbles/topolist"
	"code.byted.org/vecode/staircase/util/git"
)

type NewChangeListOpt struct {
	// Items must be in reverse-topological order, where parent should appear after children
	Items            []changemodel.ChangeListItem
	EnableChoice     bool
	Index            int
	EnablePagination bool
	AfterSelection   func(item changemodel.ChangeListItem)
}

func NewChangeList(opt NewChangeListOpt) ChangeList {
	l := &topolist.Model{
		ItemDelegate: ChangeListItemDelegate{},
		Items: lo.Map(opt.Items, func(item changemodel.ChangeListItem, _ int) topolist.Item {
			return item
		}),
		EnableChoice:     opt.EnableChoice,
		Index:            opt.Index,
		EnablePagination: opt.EnablePagination,
		SolidLineGraph:   true,
	}
	return ChangeList{
		list:           l,
		afterSelection: opt.AfterSelection,
	}
}

type ChangeList struct {
	list           *topolist.Model
	quitting       bool
	afterSelection func(item changemodel.ChangeListItem)
}

func (m ChangeList) Init() tea.Cmd {
	return nil
}

func (m ChangeList) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q", "esc":
			m.quitting = true
			return m, tea.Quit
		case "enter":
			if len(m.list.Items) == 0 {
				return m, nil
			}
			if m.afterSelection != nil {
				m.afterSelection(m.list.Items[m.list.Index].(changemodel.ChangeListItem))
			}
			m.quitting = true
			return m, tea.Quit
		}
	}
	mm, cmd := m.list.Update(msg)
	m.list = mm.(*topolist.Model)
	return m, cmd
}

func (m ChangeList) View() string {
	if m.quitting {
		return ""
	}
	return m.list.View()
}

type ChangeListItemDelegate struct{}

func (d ChangeListItemDelegate) Render(m topolist.Model, index int, item topolist.Item) string {
	t, ok := item.(changemodel.ChangeListItem)
	if !ok {
		return ""
	}
	selected := m.EnableChoice && (m.Index == index)
	if t.Change != nil {
		return d.RenderChange(t.Change, m.EnableChoice, selected, false)
	}
	return d.RenderBranch(t.Branch, m.EnableChoice, selected, false)
}

func (d ChangeListItemDelegate) RenderChange(c *changemodel.Change, enableChoice bool, selected, detail bool) string {
	// ansi 16 color codes:  https://jeffkreeftmeijer.com/vim-16-color/
	titleStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("3")) // ansi bright yellow
	infoStyle := lipgloss.NewStyle()

	var titlePrefix string
	if enableChoice {
		if selected {
			titlePrefix = "▶"
		} else {
			titlePrefix = " "
		}
	}
	var infoPrefix string
	if enableChoice {
		infoPrefix = " "
	}

	lines := []string{
		titleStyle.Render(titlePrefix + c.Title),
		infoStyle.Render(infoPrefix + "ID: " + string(c.ID)),
		infoStyle.Render(infoPrefix + "Commit: " + c.LatestVersion().CommitID),
		infoStyle.Render(infoPrefix + "Base: " + c.LatestVersion().BaseCommitID),
		infoStyle.Render(infoPrefix + "Date: " + c.CreatedAt.Local().Format(time.RFC3339)),
	}
	if c.LocalBranchName != nil {
		lines = append(lines, infoStyle.Render(infoPrefix+"Branch: "+*c.LocalBranchName))
	}

	for _, ca := range c.CustomAttributes {
		name := ca.Name
		if ca.DisplayName != nil {
			name = *ca.DisplayName
		}
		lines = append(lines, infoStyle.Render(infoPrefix+name+": "+ca.Value))
	}

	return lipgloss.JoinVertical(lipgloss.Top, lines...)
}

func (d ChangeListItemDelegate) RenderBranch(branch *git.Branch, enableChoice, selected, detail bool) string {
	titleStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("3")) // ansi bright yellow
	infoStyle := lipgloss.NewStyle()

	var titlePrefix string
	if enableChoice {
		if selected {
			titlePrefix = "▶"
		} else {
			titlePrefix = " "
		}
	}
	var infoPrefix string
	if enableChoice {
		infoPrefix = " "
	}
	return lipgloss.JoinVertical(
		lipgloss.Top,
		titleStyle.Render(titlePrefix+branch.Name),
		infoStyle.Render(infoPrefix+"Commit: "+branch.Commit.ID),
		infoStyle.Render(infoPrefix+"Date: "+branch.Commit.Committer.When.Local().Format(time.RFC3339)),
	)
}
