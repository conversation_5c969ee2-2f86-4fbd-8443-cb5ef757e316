package cmd

import (
	"context"

	"github.com/spf13/cobra"
	"go.uber.org/fx"

	"code.byted.org/vecode/staircase/cmd/builtin"
	"code.byted.org/vecode/staircase/cmd/completer"
)

var Module = fx.Options(
	fx.Provide(NewRootCommand),
	builtin.Module,
	fx.Options(completer.Options...),
	fx.Invoke(func(lc fx.Lifecycle, shutdowner fx.Shutdowner, root *cobra.Command) {
		lc.Append(fx.Hook{
			OnStart: func(ctx context.Context) error {
				go func() {
					err := root.Execute()
					var opts []fx.ShutdownOption
					if err != nil {
						opts = append(opts, fx.ExitCode(1))
					}
					_ = shutdowner.Shutdown(opts...)
				}()
				return nil
			},
		})
	}),
)
