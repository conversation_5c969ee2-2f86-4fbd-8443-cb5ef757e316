package builtin

import (
	"github.com/spf13/cobra"

	changeusecase "code.byted.org/vecode/staircase/module/change/usecase"
	cmdutil "code.byted.org/vecode/staircase/util/cmd"
)

type FoldCommand struct {
	FoldUsecase changeusecase.FoldUsecase
}

func (c *FoldCommand) Run(cmd *cobra.Command, args []string) error {
	return c.FoldUsecase.Do(cmd.Context(), &changeusecase.FoldOpt{})
}

func (c *FoldCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:   "fold",
		Short: "Fold current change into its parent",
		Long: cmdutil.RenderHelp(`Fold will cherry-pick current change's commits onto its parent and remove current change.

It's recommended to run

  st squash

after fold.`),
		RunE:              c.Run,
		GroupID:           GroupIDStack,
		ValidArgsFunction: cobra.NoFileCompletions,
	}
	command.AddCommand(cmd)
	return nil
}
