package builtin

import (
	"os/exec"

	"github.com/spf13/cobra"
)

var ForwardedGitCommands = []string{
	"clone",
	"init",
	"add",
	"mv",
	"restore",
	"rm",
	"bisect",
	"diff",
	"grep",
	"branch",
	"merge",
	"reset",
	"switch",
	"tag",
	"fetch",
	"pull",
	"push",
	"stash",
}

type GitProxyCommand struct{}

func (c *GitProxyCommand) Run(cmd *cobra.Command, args []string) error {
	args = append([]string{cmd.Use}, args...)
	command := exec.CommandContext(cmd.Context(), "git", args...)
	command.Stdin = cmd.InOrStdin()
	command.Stdout = cmd.OutOrStdout()
	command.Stderr = cmd.ErrOrStderr()
	return command.Run()
}

func (c *GitProxyCommand) Register(command *cobra.Command) error {
	for _, name := range ForwardedGitCommands {
		command.AddCommand(&cobra.Command{
			Use:                name,
			Hidden:             true,
			RunE:               c.Run,
			DisableFlagParsing: true,
		})
	}
	return nil
}
