package builtin

import (
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cobra"

	"code.byted.org/vecode/staircase/cmd/bubbles"
	changedal "code.byted.org/vecode/staircase/module/change/dal"
	changemodel "code.byted.org/vecode/staircase/module/change/model"
	changeservice "code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/util/git"
	"code.byted.org/vecode/staircase/util/pager"
)

type LogCommand struct {
	ListChangesService    changeservice.ListChangesService
	RefreshChangesService changeservice.RefreshChangesService
	Git                   git.Git
	ChangeDAO             changedal.ChangeDAO
}

func (c *LogCommand) Run(cmd *cobra.Command, args []string) error {
	ctx := cmd.Context()
	// Get current work tree
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &changeservice.RefreshChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessage(err, "refresh changes failed")
	}
	// list changes
	changeList, err := c.ListChangesService.Do(ctx, &changeservice.ListChangesOpt{
		RepoPath:                    workTree,
		WithStackBottomTargetBranch: true,
	})
	if err != nil {
		return err
	}
	items := lo.Reverse(changeList)
	// locate current index
	var initialIndex *int
	{
		// get current branch
		if branch, err := c.Git.GetCurrentBranch(ctx, workTree); errors.Is(err, git.ErrBranchNotFound) {
			// do nothing
		} else if err != nil {
			return errors.WithMessage(err, "get current branch failed")
		} else {
			_, index, found := lo.FindIndexOf(items, func(item changemodel.ChangeListItem) bool {
				return item.Branch != nil && item.Branch.Name == branch
			})
			if found {
				initialIndex = &index
			} else { // try to locate by change
				// find change by current branch
				if change, err := c.ChangeDAO.GetChangeByLocalBranch(ctx, workTree, branch); errors.Is(err, changedal.ErrChangeNotFound) {
					// do nothing
				} else if err != nil {
					return errors.WithMessage(err, "get change failed")
				} else {
					_, index, found := lo.FindIndexOf(items, func(item changemodel.ChangeListItem) bool {
						return item.Change != nil && item.Change.ID == change.ID
					})
					if found {
						initialIndex = &index
					}
				}
			}

		}
	}
	var m bubbles.ChangeList
	if initialIndex == nil {
		m = bubbles.NewChangeList(bubbles.NewChangeListOpt{
			Items: items,
		})
	} else {
		m = bubbles.NewChangeList(bubbles.NewChangeListOpt{
			Items:        items,
			EnableChoice: true,
			Index:        *initialIndex,
		})
	}
	return pager.Show(m.View())
}

func (c *LogCommand) Register(command *cobra.Command) error {
	command.AddCommand(&cobra.Command{
		Use:               "log",
		Short:             "List changes and their branches",
		RunE:              c.Run,
		GroupID:           GroupIDView,
		ValidArgsFunction: cobra.NoFileCompletions,
	})
	return nil
}
