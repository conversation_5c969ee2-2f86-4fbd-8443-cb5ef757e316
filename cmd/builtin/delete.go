package builtin

import (
	"github.com/pkg/errors"
	"github.com/spf13/cobra"

	"code.byted.org/vecode/staircase/cmd/completer"
	changedal "code.byted.org/vecode/staircase/module/change/dal"
	changemodel "code.byted.org/vecode/staircase/module/change/model"
	changeservice "code.byted.org/vecode/staircase/module/change/service"
	changeusecase "code.byted.org/vecode/staircase/module/change/usecase"
	"code.byted.org/vecode/staircase/util/git"
)

type DeleteCommand struct {
	Git                   git.Git
	ChangeDAO             changedal.ChangeDAO
	RefreshChangesService changeservice.RefreshChangesService
	RebaseService         changeservice.RebaseService
	RebaseUsecase         changeusecase.RebaseUsecase
	ListChangesService    changeservice.ListChangesService
	ChangeListCompleter   *completer.ChangeListCompleter
}

func (c *DeleteCommand) Run(cmd *cobra.Command, args []string) error {
	ctx := cmd.Context()
	// get current work tree
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &changeservice.RefreshChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessage(err, "refresh changes failed")
	}
	// check worktree
	clean, err := c.Git.IsWorktreeClean(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "failed to check work tree")
	}
	if !clean {
		return errors.New("work tree not clean")
	}

	// get current branch
	branch, err := c.Git.GetCurrentBranch(ctx, workTree)
	if errors.Is(err, git.ErrBranchNotFound) {
		branch = ""
	} else if err != nil {
		return errors.WithMessage(err, "get current branch failed")
	}

	// find change to delete
	var change *changemodel.Change
	if len(args) == 0 {
		// find change by current branch
		change, err = c.ChangeDAO.GetChangeByLocalBranch(ctx, workTree, branch)
		if errors.Is(err, changedal.ErrChangeNotFound) {
			return nil
		} else if err != nil {
			return errors.WithMessage(err, "get change failed")
		}
	} else {
		change, err = c.ChangeDAO.GetChange(ctx, workTree, changemodel.ChangeID(args[0]))
		if errors.Is(err, changedal.ErrChangeNotFound) {
			return nil
		} else if err != nil {
			return errors.WithMessage(err, "get change failed")
		}
	}
	// list all changes
	changeList, err := c.ListChangesService.Do(ctx, &changeservice.ListChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessage(err, "list changes failed")
	}
	changes := changeList.Changes()
	// modify graph
	g := (*changemodel.ChangeGraph)(&changes)
	updatedChanges, err := g.Remove(change.ID)
	if err != nil {
		return errors.WithMessage(err, "failed to remove change from graph")
	}
	// build rebase
	rebase := &changemodel.Rebase{
		BranchNameBeforeStart: branch,
		Actions:               changemodel.NewRebaseActionsFromChanges(g.UpstackChanges(updatedChanges...), false),
		Remove:                []changemodel.ChangeID{change.ID},
	}
	if change.ParentID == nil {
		rebase.Checkouts = []changemodel.Checkout{
			{
				Revision: change.TargetBranchName,
			},
		}
	} else {
		rebase.Checkouts = []changemodel.Checkout{
			{
				ChangeID: change.ParentID,
			},
		}
	}
	// rebase
	err = c.RebaseService.Do(ctx, &changeservice.RebaseOpt{
		RepoPath: workTree,
		Rebase:   rebase,
	})
	if ce := new(changeservice.RebaseConflictError); errors.As(err, &ce) {
		return c.RebaseUsecase.HandleConflict(ctx, &changeusecase.HandleConflictOpt{
			RepoPath:      workTree,
			ConflictError: ce,
		})
	}
	if err != nil {
		return errors.WithMessage(err, "rebase failed")
	}
	// update worktree
	err = c.RebaseUsecase.UpdateWorktreeAfterRebase(ctx, &changeusecase.UpdateWorktreeAfterRebaseOpt{
		RepoPath: workTree,
		Rebase:   rebase,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to update worktree")
	}
	return nil
}

func (c *DeleteCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:               "delete",
		Short:             "Delete a change and perform a restack",
		RunE:              c.Run,
		GroupID:           GroupIDStack,
		ValidArgsFunction: c.ChangeListCompleter.CompleteWithOutBranches,
	}
	command.AddCommand(cmd)
	return nil
}
