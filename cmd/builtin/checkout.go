package builtin

import (
	"github.com/AlekSi/pointer"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cobra"

	"code.byted.org/vecode/staircase/cmd/bubbles"
	"code.byted.org/vecode/staircase/cmd/completer"
	changedal "code.byted.org/vecode/staircase/module/change/dal"
	changemodel "code.byted.org/vecode/staircase/module/change/model"
	changeservice "code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/util/git"
)

type CheckoutCommand struct {
	ChangeListCompleter   *completer.ChangeListCompleter
	Git                   git.Git
	ListChangesService    changeservice.ListChangesService
	RefreshChangesService changeservice.RefreshChangesService
	ChangeDAO             changedal.ChangeDAO
}

func (c *CheckoutCommand) Run(cmd *cobra.Command, args []string) error {
	// Get changes
	ctx := cmd.Context()
	// Get current work tree
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &changeservice.RefreshChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessage(err, "refresh changes failed")
	}
	// get change or revision to checkout
	var (
		changeToCheckout   *changemodel.Change
		revisionToCheckout *string
	)
	switch len(args) {
	case 1: // direct checkout
		if changeID := changemodel.ChangeID(args[0]); changeID.Valid() {
			change, err := c.ChangeDAO.GetChange(ctx, workTree, changeID)
			if err != nil {
				return errors.WithMessage(err, "failed to get change")
			}
			changeToCheckout = change
		} else {
			revisionToCheckout = &args[0]
		}
	case 0: // interactive checkout
		// prepare changes and branches to show user
		changeList, err := c.ListChangesService.Do(ctx, &changeservice.ListChangesOpt{
			RepoPath:                    workTree,
			WithStackBottomTargetBranch: true,
		})
		if err != nil {
			return err
		}
		if len(changeList) == 0 {
			defaultBranchName, err := c.Git.GetDefaultBranchName(ctx, workTree, git.DefaultRemote)
			if errors.Is(err, git.ErrBranchNotFound) {
				return nil
			}
			branch, err := c.Git.GetBranch(ctx, workTree, defaultBranchName)
			if err != nil {
				return errors.WithMessagef(err, "failed to get branch(%s)", defaultBranchName)
			}
			changeList = []changemodel.ChangeListItem{
				{
					Branch: branch,
				},
			}
		}
		items := lo.Reverse(changeList)
		// locate current index
		var initialIndex int
		{
			// get current branch
			if branch, err := c.Git.GetCurrentBranch(ctx, workTree); errors.Is(err, git.ErrBranchNotFound) {
				// do nothing
			} else if err != nil {
				return errors.WithMessage(err, "get current branch failed")
			} else {
				_, index, found := lo.FindIndexOf(items, func(item changemodel.ChangeListItem) bool {
					return item.Branch != nil && item.Branch.Name == branch
				})
				if found {
					initialIndex = index
				} else { // try to locate by change
					// find change by current branch
					if change, err := c.ChangeDAO.GetChangeByLocalBranch(ctx, workTree, branch); errors.Is(err, changedal.ErrChangeNotFound) {
						// do nothing
					} else if err != nil {
						return errors.WithMessage(err, "get change failed")
					} else {
						_, index, found := lo.FindIndexOf(items, func(item changemodel.ChangeListItem) bool {
							return item.Change != nil && item.Change.ID == change.ID
						})
						if found {
							initialIndex = index
						}
					}
				}

			}
		}

		// start an interactive checkout
		var choice *changemodel.ChangeListItem
		m := bubbles.NewChangeList(bubbles.NewChangeListOpt{
			Items:            items,
			EnableChoice:     true,
			Index:            initialIndex,
			EnablePagination: true,
			AfterSelection: func(item changemodel.ChangeListItem) {
				choice = &item
			},
		})
		_, err = tea.NewProgram(m, tea.WithAltScreen()).Run()
		if err != nil {
			return err
		}
		if choice == nil {
			return nil
		}
		if choice.Change != nil {
			changeToCheckout = choice.Change
		} else if choice.Branch != nil {
			revisionToCheckout = &choice.Branch.Name
		}
	default:
		return errors.New("too many arguments")
	}

	// Checkout branch
	if change := changeToCheckout; change != nil {
		if change.LocalBranchName != nil {
			return c.Git.Checkout(ctx, workTree, *change.LocalBranchName, false)
		}
		// create branch
		branch := change.DefaultLocalBranchName()
		err := c.Git.CreateBranch(ctx, workTree, branch, change.LatestVersion().CommitID)
		if err != nil {
			return errors.WithMessagef(err, "failed to create branch(%s)", branch)
		}
		// checkout branch
		err = c.Git.Checkout(ctx, workTree, branch, false)
		if err != nil {
			return err
		}
		// update change
		change.LocalBranchName = pointer.To(change.DefaultLocalBranchName())
		_, err = c.ChangeDAO.SaveChange(ctx, workTree, change)
		if err != nil {
			return errors.WithMessage(err, "failed to save change")
		}
		return nil
	}
	if revisionToCheckout != nil {
		return c.Git.Checkout(ctx, workTree, *revisionToCheckout, false)
	}

	return errors.New("failed to checkout")
}

func (c *CheckoutCommand) Register(command *cobra.Command) error {
	command.AddCommand(&cobra.Command{
		Use:               "checkout [<change-id>|<revision>]",
		Aliases:           []string{"co"},
		Short:             "Switch changes",
		RunE:              c.Run,
		GroupID:           GroupIDMove,
		Args:              cobra.MaximumNArgs(1),
		ValidArgsFunction: c.ChangeListCompleter.Complete,
	})
	return nil
}
