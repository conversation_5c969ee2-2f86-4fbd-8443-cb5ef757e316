package builtin

import (
	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/spf13/cobra"

	"code.byted.org/vecode/staircase/cmd/completer"
	changemodel "code.byted.org/vecode/staircase/module/change/model"
	syncusecase "code.byted.org/vecode/staircase/module/sync/usecase"
	cmdutil "code.byted.org/vecode/staircase/util/cmd"
	"code.byted.org/vecode/staircase/util/git"
)

type SyncCommand struct {
	RemoteCompleter    *completer.RemoteCompleter
	SyncChangesUsecase syncusecase.SyncChangesUsecase
	// args
	Remote   string `fx:"-"`
	Restack  bool   `fx:"-"`
	CommitID string `fx:"-"`
}

func (c *SyncCommand) Run(cmd *cobra.Command, args []string) error {
	var id *changemodel.ChangeID
	if c.CommitID == "" && len(args) == 1 {
		id = (*changemodel.ChangeID)(&args[0])
	}
	return c.SyncChangesUsecase.Do(cmd.Context(), &syncusecase.SyncChangesOpt{
		Remote:         c.Remote,
		Restack:        c.Restack,
		CommitID:       pointer.ToOrNil(c.CommitID),
		ID:             id,
		ProgressWriter: cmd.ErrOrStderr(),
	})
}

func (c *SyncCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:   "sync [<change-id>]",
		Short: "Sync changes from remote",
		Long: cmdutil.RenderHelp(`Fetch code and update local change graph based on remote status.

By default every local change will be synced. To sync a specific change or a change that doesn't exist locally, you can use 'st sync <change-id>' or 'st sync -c <commit-id>'.

Dependencies will be synced automatically.

Base branches will be updated to the latest status of their upstream branches.

By default a restack is performed after sync. If a change becomes empty after restack, it will be removed automatically.`),
		RunE:    c.Run,
		GroupID: GroupIDSync,
		Args:    cobra.MaximumNArgs(1),
	}
	cmd.Flags().StringVarP(&c.Remote, "remote", "r", git.DefaultRemote, "name of the remote to sync changes")
	err := cmd.RegisterFlagCompletionFunc("remote", c.RemoteCompleter.Complete)
	if err != nil {
		return errors.WithMessage(err, "failed to register flag completion func for '--remote'")
	}
	cmd.Flags().BoolVarP(&c.Restack, "restack", "", true, "whether to restack after sync")

	cmd.Flags().StringVarP(&c.CommitID, "commit-id", "c", "", "sync remote changes with the specified latest commit id")
	err = cmd.RegisterFlagCompletionFunc("commit-id", cobra.NoFileCompletions)
	if err != nil {
		return errors.WithMessage(err, "failed to register flag completion func for '--commit-id'")
	}

	command.AddCommand(cmd)
	return nil
}
