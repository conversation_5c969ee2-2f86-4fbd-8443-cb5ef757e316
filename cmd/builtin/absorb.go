package builtin

import (
	"github.com/spf13/cobra"

	changeusecase "code.byted.org/vecode/staircase/module/change/usecase"
	cmdutil "code.byted.org/vecode/staircase/util/cmd"
)

type AbsorbCommand struct {
	AbsorbUsecase changeusecase.AbsorbUsecase
	// args
	DryRun       bool `fx:"-"`
	ApplyChanges bool `fx:"-"`
}

func (c *AbsorbCommand) Run(cmd *cobra.Command, args []string) error {
	return c.AbsorbUsecase.Do(cmd.Context(), &changeusecase.AbsorbOpt{
		DryRun:       c.DryRun,
		ApplyChanges: c.ApplyChanges,
	})
}

func (c *AbsorbCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:   "absorb",
		Short: "Intelligently integrate pending changes into current stack",
		Long: cmdutil.RenderHelp(`Attempt to amend each staged hunk (consecutive lines of patch) to the proper change in your stack.

For each staged hunk, absorb will check each change downstack from the current change, trying to find the first change to which that hunk cannot be applied cleanly. If such change is found, that hunk will be amended into that change. Otherwise that hunk will remain uncommitted in the index.

By default, absorb will show what it plans to do and prompt for confirmation. If you are confident that the changes will be absorbed to the correct place, run 

  st absorb -a

to apply the changes immediately.

You can also use 

  st absorb --dry-run

to only view the plan.`),
		Example: cmdutil.RenderHelp(`Let's assume we have two changes in our stack, 'change1' and 'change2'. 'change2' relies on 'change1'. 'change1' added one file named 'file1', 'change2' added one file named 'file2'.

We have checked out 'change2', and have made some changes to both 'file1' and 'file2'. Now we want to add changes in 'file1' back to 'change1', changes in 'file2' back to 'change2'. So we can run

  st add . && st absorb

It will show us what will be changed, and prompt us to confirm. After everything is checked, we can type 'y' to apply those changes.
`),
		RunE:              c.Run,
		GroupID:           GroupIDStack,
		ValidArgsFunction: cobra.NoFileCompletions,
	}
	cmd.Flags().BoolVar(&c.DryRun, "dry-run", false, "do not perform actions, just show changes")
	cmd.Flags().BoolVarP(&c.ApplyChanges, "apply-changes", "a", false, "apply changes without prompting for confirmation")
	command.AddCommand(cmd)
	return nil
}
