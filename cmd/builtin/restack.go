package builtin

import (
	"github.com/spf13/cobra"

	"code.byted.org/vecode/staircase/module/change/usecase"
	cmdutil "code.byted.org/vecode/staircase/util/cmd"
)

type RestackCommand struct {
	RebaseUsecase usecase.RebaseUsecase
}

func (c *RestackCommand) Run(cmd *cobra.Command, _ []string) error {
	return c.RebaseUsecase.Do(cmd.Context(), &usecase.RebaseOpt{})
}

func (c *RestackCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:               "restack",
		Short:             "Rebase changes onto the latest version of their respective parents",
		Long:              cmdutil.RenderHelp("This is a shortcut for rebase without any arguments or flags."),
		RunE:              c.Run,
		GroupID:           GroupIDStack,
		ValidArgsFunction: cobra.NoFileCompletions,
	}
	command.AddCommand(cmd)
	return nil
}
