package builtin

import (
	"github.com/spf13/cobra"
)

const (
	GroupIDStack    = "stack"
	GroupIDUndo     = "undo"
	GroupIDSync     = "sync"
	GroupIDView     = "view"
	GroupIDChange   = "change"
	GroupIDMove     = "move"
	GroupIDConflict = "conflict"
	GroupIDWorktree = "worktree"
)

type Registrant struct{}

func (*Registrant) Register(cmd *cobra.Command) error {
	cmd.AddGroup(
		&cobra.Group{
			ID:    GroupIDChange,
			Title: "Create and modify changes:",
		},
		&cobra.Group{
			ID:    GroupIDView,
			Title: "View changes:",
		},
		&cobra.Group{
			ID:    GroupIDMove,
			Title: "Move between changes:",
		},
		&cobra.Group{
			ID:    GroupIDWorktree,
			Title: "Work with worktree",
		},
		&cobra.Group{
			ID:    GroupIDStack,
			Title: "Work with a stack of changes:",
		},
		&cobra.Group{
			ID:    GroupIDSync,
			Title: "Sync with remote:",
		},
		&cobra.Group{
			ID:    GroupIDUndo,
			Title: "Undo commands:",
		},
		&cobra.Group{
			ID:    GroupIDConflict,
			Title: "Work with conflicts:",
		},
	)
	return nil
}
