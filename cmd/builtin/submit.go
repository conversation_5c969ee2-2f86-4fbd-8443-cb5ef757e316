package builtin

import (
	"github.com/pkg/errors"
	"github.com/spf13/cobra"

	"code.byted.org/vecode/staircase/cmd/completer"
	syncusecase "code.byted.org/vecode/staircase/module/sync/usecase"
	cmdutil "code.byted.org/vecode/staircase/util/cmd"
	"code.byted.org/vecode/staircase/util/git"
)

type SubmitCommand struct {
	RemoteCompleter            *completer.RemoteCompleter
	SubmitChangesUsecase       syncusecase.SubmitChangesUsecase
	SubmitOptionCompletionFunc func(*cobra.Command, []string, string) ([]string, cobra.ShellCompDirective) `name:"submit_option" optional:"true"`
	// args
	Remote        string   `fx:"-"`
	SubmitOptions []string `fx:"-"`
}

func (c *SubmitCommand) Run(cmd *cobra.Command, args []string) error {
	return c.SubmitChangesUsecase.Do(cmd.Context(), &syncusecase.SubmitChangesOpt{
		Remote:         c.Remote,
		Options:        c.SubmitOptions,
		ProgressWriter: cmd.ErrOrStderr(),
	})
}

func (c *SubmitCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:   "submit",
		Short: "Submit changes to remote",
		Long: cmdutil.RenderHelp(`Submit changes in the current downstack to remote. Push their code and create remote counterparts based on local change graph.

You can use -r to decide which remote to submit changes to.

You can pass multiple -o flags to customize remote behavior.`),
		RunE:              c.Run,
		GroupID:           GroupIDSync,
		ValidArgsFunction: cobra.NoFileCompletions,
	}

	cmd.Flags().StringSliceVarP(&c.SubmitOptions, "submit-option", "o", nil, "custom option for remote server")
	submitOptionCompletionFunc := c.SubmitOptionCompletionFunc
	if submitOptionCompletionFunc == nil {
		submitOptionCompletionFunc = cobra.NoFileCompletions
	}
	err := cmd.RegisterFlagCompletionFunc("submit-option", submitOptionCompletionFunc)
	if err != nil {
		return err
	}

	cmd.Flags().StringVarP(&c.Remote, "remote", "r", git.DefaultRemote, "name of the remote to submit changes")
	err = cmd.RegisterFlagCompletionFunc("remote", c.RemoteCompleter.Complete)
	if err != nil {
		return errors.WithMessage(err, "failed to register flag completion func for '--remote'")
	}

	command.AddCommand(cmd)
	return nil
}
