package builtin

import (
	"io"

	"github.com/pkg/errors"
	"github.com/spf13/cobra"

	changeservice "code.byted.org/vecode/staircase/module/change/service"
	cmdutil "code.byted.org/vecode/staircase/util/cmd"
	"code.byted.org/vecode/staircase/util/git"
)

type StatusCommand struct {
	Git                      git.Git
	RefreshChangesService    changeservice.RefreshChangesService
	GetWorktreeStatusService changeservice.GetWorktreeStatusService
}

func (c *StatusCommand) Run(cmd *cobra.Command, _ []string) error {
	ctx := cmd.Context()
	// get current work tree
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &changeservice.RefreshChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessage(err, "refresh changes failed")
	}
	// get git status
	status, err := c.GetWorktreeStatusService.Do(ctx, workTree)
	if err != nil {
		return errors.WithMessage(err, "failed to get worktree status")
	}
	// get current path from repo root
	currentPathFromRepoRoot, err := c.Git.GetPathFromRepoRoot(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "failed to get current path from repo root")
	}
	// make status paths relative
	status = status.Rel(currentPathFromRepoRoot)
	// print status
	_, _ = io.WriteString(cmd.OutOrStdout(), status.Format())
	return nil
}

func (c *StatusCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:               "status",
		Aliases:           []string{"st"},
		Short:             "Show the working tree status",
		Long:              cmdutil.RenderHelp(`Shows current branch, whether you are in the middle of a rebase, changed files, conflicted files, and gives advice.`),
		RunE:              c.Run,
		GroupID:           GroupIDWorktree,
		ValidArgsFunction: cobra.NoFileCompletions,
	}
	command.AddCommand(cmd)
	return nil
}
