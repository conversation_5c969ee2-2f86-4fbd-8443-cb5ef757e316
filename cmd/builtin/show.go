package builtin

import (
	"io"
	"os"
	"os/exec"

	"github.com/pkg/errors"
	"github.com/spf13/cobra"
	"golang.org/x/term"

	"code.byted.org/vecode/staircase/cmd/bubbles"
	changedal "code.byted.org/vecode/staircase/module/change/dal"
	changeservice "code.byted.org/vecode/staircase/module/change/service"
	executil "code.byted.org/vecode/staircase/util/exec"
	"code.byted.org/vecode/staircase/util/git"
	"code.byted.org/vecode/staircase/util/pager"
)

type ShowCommand struct {
	Git                   git.Git
	ChangeDAO             changedal.ChangeDAO
	RefreshChangesService changeservice.RefreshChangesService
	// args
	Stat bool `fx:"-"`
}

func (c *ShowCommand) Run(cmd *cobra.Command, args []string) error {
	ctx := cmd.Context()
	// get current work tree
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &changeservice.RefreshChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessage(err, "refresh changes failed")
	}

	// get current branch
	branch, err := c.Git.GetCurrentBranch(ctx, workTree)
	if errors.Is(err, git.ErrBranchNotFound) {
		branch = ""
	} else if err != nil {
		return errors.WithMessage(err, "get current branch failed")
	}
	// find change by current branch
	change, err := c.ChangeDAO.GetChangeByLocalBranch(ctx, workTree, branch)
	if errors.Is(err, changedal.ErrChangeNotFound) {
		return nil
	} else if err != nil {
		return errors.WithMessage(err, "get change failed")
	}
	// setup color and pager
	color := "never"
	out := cmd.OutOrStdout()
	if f, ok := out.(*os.File); ok && term.IsTerminal(int(f.Fd())) {
		color = "always"
		// start pager
		pagerIn, pagerCmd, err := pager.Start()
		if err != nil {
			return errors.WithMessage(err, "failed to start pager")
		}
		defer func() {
			_ = pagerIn.Close()
			_ = pagerCmd.Wait()
		}()
		out = pagerIn
	}
	// show metadata
	_, _ = io.WriteString(out, bubbles.ChangeListItemDelegate{}.RenderChange(change, false, false, false))
	_, _ = io.WriteString(out, "\n\n")
	// show commits
	{
		cmd := exec.Command("git", "log", "--color="+color, "--oneline", change.LatestVersion().RevisionRange())
		cmd.Stdout = out
		if err := executil.Run(cmd); err != nil {
			return errors.WithMessage(err, "failed to show commits")
		}
		_, _ = io.WriteString(out, "\n")
	}
	// show diff
	{
		args := []string{"diff", "--color=" + color, "--merge-base"}
		if c.Stat {
			args = append(args, "--stat")
		}
		args = append(args, change.LatestVersion().BaseCommitID, change.LatestVersion().CommitID)
		cmd := exec.Command("git", args...)
		cmd.Stdout = out
		if err := executil.Run(cmd); err != nil {
			return errors.WithMessage(executil.WrapError(err), "failed to show diff")
		}
	}
	return nil
}

func (c *ShowCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:               "show",
		Short:             "Show a change's metadata,commits and diff",
		RunE:              c.Run,
		GroupID:           GroupIDView,
		ValidArgsFunction: cobra.NoFileCompletions,
	}
	cmd.Flags().BoolVarP(&c.Stat, "stat", "", false, "show diff stat instead")
	command.AddCommand(cmd)
	return nil
}
