package builtin

import (
	"go.uber.org/fx"

	"code.byted.org/vecode/staircase/util/cmd"
	"code.byted.org/vecode/staircase/util/fxutil"
)

var Module = fx.Options(
	fx.Provide(fxutil.StructConstructor(new(Registrant))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(Registrant), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(LogCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(LogCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(NewCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(NewCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(SubmitCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(SubmitCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(SyncCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(SyncCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(CheckoutCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(CheckoutCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(RestackCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(RestackCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(RebaseCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(RebaseCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(ShowCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(ShowCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(EditCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(EditCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(DeleteCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(DeleteCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(SquashCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(SquashCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(FoldCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(FoldCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(SplitCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(SplitCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(AbortCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(AbortCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(ContinueCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(ContinueCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(AbsorbCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(AbsorbCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(UndoCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(UndoCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(RedoCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(RedoCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(StatusCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(StatusCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(CommitCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(CommitCommand), new(cmd.Registrant)))),
	fx.Provide(fxutil.StructConstructor(new(GitProxyCommand))),
	fx.Provide(cmd.AnnotateRegistrant(fxutil.Bind(new(GitProxyCommand), new(cmd.Registrant)))),
)
