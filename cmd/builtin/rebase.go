package builtin

import (
	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/spf13/cobra"

	"code.byted.org/vecode/staircase/cmd/completer"
	changemodel "code.byted.org/vecode/staircase/module/change/model"
	"code.byted.org/vecode/staircase/module/change/usecase"
	cmdutil "code.byted.org/vecode/staircase/util/cmd"
)

type RebaseCommand struct {
	ChangeListCompleter *completer.ChangeListCompleter
	RebaseUsecase       usecase.RebaseUsecase
	// args
	Onto        string `fx:"-"`
	UpStack     bool   `fx:"-"`
	Interactive bool   `fx:"-"`
}

func (c *RebaseCommand) Run(cmd *cobra.Command, args []string) error {
	var id *changemodel.ChangeID
	if len(args) == 1 {
		id = (*changemodel.ChangeID)(&args[0])
	}
	opt := &usecase.RebaseOpt{
		ChangeID:    id,
		UpStack:     c.UpStack,
		Onto:        pointer.ToOrNil(c.Onto),
		Interactive: c.Interactive,
	}
	return c.RebaseUsecase.Do(cmd.Context(), opt)
}

func (c *RebaseCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:   "rebase",
		Short: "Move changes from one location to another",
		Long: cmdutil.RenderHelp(`Rebase acts like cherry-picking commits of a change one by one to another location, so conflicts happen less frequently than a normal git rebase.

A rebase without any arguments and flags will rebase every change in the current stack onto the latest version of its parent, that is, a restack.

If you want to rebase current stack to another change or branch, use the --onto flag.

If you want to rebase only changes upstack from current change, use the --upstack flag.

If you want to manually adjust the structure of change graph, use the -i flag and follow instructions there.`),
		Example: cmdutil.RenderHelp(`If we have a change graph like this:

    c
    |
  a b
  |/
  base

and we are on change 'b', then the command
  st rebase --onto <change-id-of-a>
would result in:

  c
  |
  b
  |
  a
  |
  base

This is useful when we already have 'b' and 'c', but realize that a new fundamental dependency 'a' must be added.

If we have a change graph like this:

  c
  |
  b 
  |
  a
  |
  base

and we are on change 'b', then the command
  st rebase --onto base --upstack
would result in:

    c
    |
  a b
  |/
  base

This is useful when we realize 'a' is not really a dependency of 'b' and 'c''.
`),
		RunE:    c.Run,
		GroupID: GroupIDStack,
	}
	cmd.Flags().StringVarP(&c.Onto, "onto", "", "", "new base to rebase onto, can be a branch name or a change id")
	err := cmd.RegisterFlagCompletionFunc("onto", c.ChangeListCompleter.Complete)
	if err != nil {
		return errors.WithMessage(err, "failed to register flag completion func for '--onto'")
	}
	cmd.Flags().BoolVarP(&c.UpStack, "upstack", "", false, "only rebase changes upstack")
	cmd.Flags().BoolVarP(&c.Interactive, "interactive", "i", false, "launch an editor to edit rebase plan")
	command.AddCommand(cmd)
	return nil
}
