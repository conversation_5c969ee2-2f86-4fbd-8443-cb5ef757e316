package builtin

import (
	"github.com/spf13/cobra"

	"code.byted.org/vecode/staircase/module/change/usecase"
)

type AbortCommand struct {
	RebaseUsecase usecase.RebaseUsecase
}

func (c *AbortCommand) Run(cmd *cobra.Command, args []string) error {
	return c.RebaseUsecase.Abort(cmd.Context())
}

func (c *AbortCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:               "abort",
		Short:             "Abort the most recent command halted by a rebase conflict",
		RunE:              c.<PERSON>,
		GroupID:           GroupIDConflict,
		ValidArgsFunction: cobra.NoFileCompletions,
	}
	command.AddCommand(cmd)
	return nil
}
