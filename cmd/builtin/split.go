package builtin

import (
	"github.com/spf13/cobra"

	changeusecase "code.byted.org/vecode/staircase/module/change/usecase"
	cmdutil "code.byted.org/vecode/staircase/util/cmd"
)

type SplitCommand struct {
	SplitUsecase changeusecase.SplitUsecase
	// args
	ByCommits bool `fx:"-"`
}

func (c *SplitCommand) Run(cmd *cobra.Command, args []string) error {
	ctx := cmd.Context()
	return c.SplitUsecase.Do(ctx, &changeusecase.SplitOpt{
		ByCommits: c.ByCommits,
	})
}

func (c *SplitCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:   "split",
		Short: "Split current change into multiple changes",
		Long: cmdutil.RenderHelp(`If a change becomes too large, you can break it into smaller pieces.

By default, split is done by performing a series of interactive git-adds. However if there are already a set of well-organized commits, you can use -c flag to split by commits.`),
		RunE:              c.Run,
		GroupID:           GroupIDStack,
		ValidArgsFunction: cobra.NoFileCompletions,
	}
	cmd.Flags().BoolVarP(&c.ByCommits, "commits", "c", false, "split change by commits")
	command.AddCommand(cmd)
	return nil
}
