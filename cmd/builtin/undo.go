package builtin

import (
	"github.com/spf13/cobra"

	changeusecase "code.byted.org/vecode/staircase/module/change/usecase"
	cmdutil "code.byted.org/vecode/staircase/util/cmd"
)

type UndoCommand struct {
	UndoUsecase changeusecase.UndoUsecase
	// args
	Interactive bool `fx:"-"`
}

func (c *UndoCommand) Run(cmd *cobra.Command, _ []string) error {
	return c.UndoUsecase.Do(cmd.Context(), &changeusecase.UndoOpt{
		Interactive: c.Interactive,
	})
}

func (c *UndoCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:   "undo",
		Short: "Reverse the local effects of a command",
		Long: cmdutil.RenderHelp(`Local effects means updates of local changes and their branches. Remote status changes made by 'submit' cannot be undone.

You can undo multiple times to undo a series of commands.

You can also use 'st undo -i' to interactively select a command to undo and rollback to the status before that command.`),
		RunE:              c.Run,
		GroupID:           GroupIDUndo,
		ValidArgsFunction: cobra.NoFileCompletions,
	}
	cmd.Flags().BoolVarP(&c.Interactive, "interactive", "i", false, "use interactive ui to choose which command to undo")
	command.AddCommand(cmd)
	return nil
}
