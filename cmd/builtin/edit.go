package builtin

import (
	"github.com/AlekSi/pointer"
	"github.com/pkg/errors"
	"github.com/spf13/cobra"

	changedal "code.byted.org/vecode/staircase/module/change/dal"
	changemodel "code.byted.org/vecode/staircase/module/change/model"
	changeservice "code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/util/editor"
	"code.byted.org/vecode/staircase/util/git"
	yamlutil "code.byted.org/vecode/staircase/util/yaml"
)

const ChangeEditFormFileNameTemplate = "change-*.yaml"

type ChangeEditForm struct {
	Title yamlutil.SingleQuotedString `yaml:"Title"`
}

func (f *ChangeEditForm) UpdateChange(c *changemodel.Change) {
	c.Title = string(f.Title)
}

func NewChangeEditForm(c *changemodel.Change) *ChangeEditForm {
	return &ChangeEditForm{
		Title: yamlutil.SingleQuotedString(c.Title),
	}
}

type EditCommand struct {
	Git                   git.Git
	ChangeDAO             changedal.ChangeDAO
	RefreshChangesService changeservice.RefreshChangesService
}

func (c *EditCommand) Run(cmd *cobra.Command, args []string) error {
	ctx := cmd.Context()
	// get current work tree
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		return errors.WithMessage(err, "not inside a git work tree")
	}
	// refresh changes
	err = c.RefreshChangesService.Do(ctx, &changeservice.RefreshChangesOpt{
		RepoPath: workTree,
	})
	if err != nil {
		return errors.WithMessage(err, "refresh changes failed")
	}

	// get current branch
	branch, err := c.Git.GetCurrentBranch(ctx, workTree)
	if errors.Is(err, git.ErrBranchNotFound) {
		branch = ""
	} else if err != nil {
		return errors.WithMessage(err, "get current branch failed")
	}
	// find change by current branch
	change, err := c.ChangeDAO.GetChangeByLocalBranch(ctx, workTree, branch)
	if errors.Is(err, changedal.ErrChangeNotFound) {
		return nil
	} else if err != nil {
		return errors.WithMessage(err, "get change failed")
	}
	// convert change to form
	form := NewChangeEditForm(change)
	// ask user to edit form
	newForm, err := editor.EditYamlWithTempFile(ChangeEditFormFileNameTemplate, form)
	if err != nil {
		return errors.WithMessage(err, "failed to edit change form")
	}
	// update change
	newChange := change.Copy()
	newForm.UpdateChange(newChange)
	if newChange.Title != change.Title && (change.LocalBranchName == nil || *change.LocalBranchName == change.DefaultLocalBranchName()) {
		newChange.LocalBranchName = pointer.To(newChange.DefaultLocalBranchName())
		// create new branch
		if *newChange.LocalBranchName != branch {
			err = c.Git.CreateBranch(ctx, workTree, *newChange.LocalBranchName, newChange.LatestVersion().CommitID)
			if err != nil {
				return errors.WithMessagef(err, "failed to create branch(%s)", *newChange.LocalBranchName)
			}
		}
	}
	// save change
	_, err = c.ChangeDAO.SaveChange(ctx, workTree, newChange)
	if err != nil {
		return errors.WithMessage(err, "save change failed")
	}
	if *newChange.LocalBranchName != branch {
		// checkout new branch
		err = c.Git.Checkout(ctx, workTree, *newChange.LocalBranchName, false)
		if err != nil {
			return errors.WithMessagef(err, "failed to checkout branch(%s)", *newChange.LocalBranchName)
		}
		// remove old branch
		err = c.Git.UpdateRef(ctx, workTree, git.BranchRefPrefix+branch, git.BlankID, nil)
		if err != nil {
			return errors.WithMessagef(err, "failed to delete branch(%s)", branch)
		}
	}
	return nil
}

func (c *EditCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:               "edit",
		Short:             "Edit change metadata",
		RunE:              c.Run,
		GroupID:           GroupIDChange,
		ValidArgsFunction: cobra.NoFileCompletions,
	}
	command.AddCommand(cmd)
	return nil
}
