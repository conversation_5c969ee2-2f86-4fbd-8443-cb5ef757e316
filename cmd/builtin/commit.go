package builtin

import (
	"github.com/spf13/cobra"

	"code.byted.org/vecode/staircase/module/change/usecase"
	cmdutil "code.byted.org/vecode/staircase/util/cmd"
)

type CommitCommand struct {
	CommitUsecase usecase.CommitUsecase
	// args
	Edit bool `fx:"-"`
}

func (c *CommitCommand) Run(cmd *cobra.Command, args []string) error {
	return c.CommitUsecase.Do(cmd.Context(), &usecase.CommitOpt{
		Edit: c.Edit,
	})
}

func (c *CommitCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:   "commit",
		Short: "Commit changes to the given files to your local repository",
		Long: cmdutil.RenderHelp(`If there are no commits in the current change, commit will create a new commit. Otherwise, commit will amend the tip commit along with its commit message instead of creating a new one.

By default, current change's title will be used as commit message. You can also use -e flag to customize using an editor.`),
		RunE:              c.Run,
		GroupID:           GroupIDChange,
		ValidArgsFunction: cobra.NoFileCompletions,
	}
	cmd.Flags().BoolVarP(&c.Edit, "edit", "e", false, "open an editor to edit commit message")
	command.AddCommand(cmd)
	return nil
}
