package builtin

import (
	"github.com/spf13/cobra"

	changeusecase "code.byted.org/vecode/staircase/module/change/usecase"
	cmdutil "code.byted.org/vecode/staircase/util/cmd"
)

type NewCommand struct {
	CreateChangeUsecase changeusecase.CreateChangeUsecase
	// args
	Title  string `fx:"-"`
	Branch string `fx:"-"`
}

func (c *NewCommand) Run(cmd *cobra.Command, args []string) error {
	opt := &changeusecase.CreateChangeOpt{
		Title:       c.Title,
		LocalBranch: c.Branch,
	}
	_, err := c.CreateChangeUsecase.Do(cmd.Context(), opt)
	return err
}

func (c *NewCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:   "new",
		Short: "Create a new change",
		Long: cmdutil.RenderHelp(`This command requires current HEAD to be a branch.

If current branch belongs to a change, it will be used as the new change's parent. Otherwise a stack-bottom change will be created using current branch as base branch.

If current branch has diverged from its upstream, its local commits will be used as the new change's commits.

By default, the normalized title will be used as the local branch name, you can use -b to customize.`),
		RunE:              c.Run,
		GroupID:           GroupIDChange,
		ValidArgsFunction: cobra.NoFileCompletions,
	}

	cmd.Flags().StringVarP(&c.Title, "title", "t", "", "title of the new change")
	err := cmd.MarkFlagRequired("title")
	if err != nil {
		return err
	}
	err = cmd.RegisterFlagCompletionFunc("title", cobra.NoFileCompletions)
	if err != nil {
		return err
	}

	cmd.Flags().StringVarP(&c.Branch, "branch", "b", "", "local branch of the new change")
	err = cmd.RegisterFlagCompletionFunc("branch", cobra.NoFileCompletions)
	if err != nil {
		return err
	}

	command.AddCommand(cmd)
	return nil
}
