package builtin

import (
	"github.com/spf13/cobra"

	"code.byted.org/vecode/staircase/module/change/usecase"
)

type ContinueCommand struct {
	RebaseUsecase usecase.RebaseUsecase
}

func (c *ContinueCommand) Run(cmd *cobra.Command, args []string) error {
	return c.RebaseUsecase.Continue(cmd.Context())
}

func (c *ContinueCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:               "continue",
		Short:             "Continue the most recent command halted by a rebase conflict",
		RunE:              c.Run,
		GroupID:           GroupIDConflict,
		ValidArgsFunction: cobra.NoFileCompletions,
	}
	command.AddCommand(cmd)
	return nil
}
