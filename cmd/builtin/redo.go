package builtin

import (
	"github.com/spf13/cobra"

	changeusecase "code.byted.org/vecode/staircase/module/change/usecase"
	cmdutil "code.byted.org/vecode/staircase/util/cmd"
)

type RedoCommand struct {
	RedoUsecase changeusecase.RedoUsecase
}

func (c *RedoCommand) Run(cmd *cobra.Command, args []string) error {
	return c.RedoUsecase.Do(cmd.Context(), &changeusecase.RedoOpt{})
}

func (c *RedoCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:   "redo",
		Short: "Reverse the effects of the last undo command",
		Long: cmdutil.RenderHelp(`You can run redo multiple times to revert a series of undo commands.

If there are N consecutive undo commands since the last command, you can redo N times.

If the last command is an interactive undo, you can redo only once.`),
		RunE:              c.Run,
		GroupID:           GroupIDUndo,
		ValidArgsFunction: cobra.NoFileCompletions,
	}
	command.AddCommand(cmd)
	return nil
}
