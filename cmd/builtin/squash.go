package builtin

import (
	"github.com/spf13/cobra"

	changeusecase "code.byted.org/vecode/staircase/module/change/usecase"
	cmdutil "code.byted.org/vecode/staircase/util/cmd"
)

type SquashCommand struct {
	SquashUsecase changeusecase.SquashUsecase
}

func (c *SquashCommand) Run(cmd *cobra.Command, args []string) error {
	return c.SquashUsecase.Do(cmd.Context(), &changeusecase.SquashOpt{})
}

func (c *SquashCommand) Register(command *cobra.Command) error {
	cmd := &cobra.Command{
		Use:               "squash",
		Short:             "Squash all commits in a change",
		Long:              cmdutil.RenderHelp(`Squash all commits for each change in the current repository, using change title as commit message.`),
		RunE:              c.Run,
		GroupID:           GroupIDChange,
		ValidArgsFunction: cobra.NoFileCompletions,
	}
	command.AddCommand(cmd)
	return nil
}
