package completer

import (
	"github.com/pkg/errors"
	"github.com/spf13/cobra"
	"go.uber.org/fx"

	changeservice "code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/util/fxutil"
	"code.byted.org/vecode/staircase/util/git"
)

func init() {
	Options = append(Options, fx.Provide(fxutil.StructConstructor(new(RemoteCompleter))))
}

type RemoteCompleter struct {
	Git                git.Git
	ListChangesService changeservice.ListChangesService
}

func (c *RemoteCompleter) Complete(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
	if len(args) != 0 {
		return nil, cobra.ShellCompDirectiveNoFileComp
	}
	ctx := cmd.Context()
	// list remote names
	remotes, err := c.Git.ListRemoteNames(ctx, ".")
	if err != nil {
		cobra.CompErrorln(errors.WithMessage(err, "failed to list remote names").Error())
		return nil, cobra.ShellCompDirectiveError
	}

	return remotes, cobra.ShellCompDirectiveKeepOrder | cobra.ShellCompDirectiveNoFileComp
}
