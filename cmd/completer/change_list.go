package completer

import (
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cobra"
	"go.uber.org/fx"

	changemodel "code.byted.org/vecode/staircase/module/change/model"
	changeservice "code.byted.org/vecode/staircase/module/change/service"
	"code.byted.org/vecode/staircase/util/fxutil"
	"code.byted.org/vecode/staircase/util/git"
)

func init() {
	Options = append(Options, fx.Provide(fxutil.StructConstructor(new(ChangeListCompleter))))
}

type ChangeListCompleter struct {
	Git                git.Git
	ListChangesService changeservice.ListChangesService
}

func (c *ChangeListCompleter) Complete(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
	return c.complete(cmd, args, toComplete, true)
}

func (c *ChangeListCompleter) CompleteWithOutBranches(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
	return c.complete(cmd, args, toComplete, false)
}

func (c *ChangeListCompleter) complete(cmd *cobra.Command, args []string, toComplete string, withBranches bool) ([]string, cobra.ShellCompDirective) {
	if len(args) != 0 {
		return nil, cobra.ShellCompDirectiveNoFileComp
	}
	ctx := cmd.Context()
	// Get current work tree
	workTree, err := c.Git.GetWorkTree(ctx, ".")
	if err != nil {
		cobra.CompErrorln(errors.WithMessage(err, "failed to get worktree").Error())
		return nil, cobra.ShellCompDirectiveError
	}

	items, err := c.ListChangesService.Do(ctx, &changeservice.ListChangesOpt{
		RepoPath:                    workTree,
		WithStackBottomTargetBranch: withBranches,
	})
	if err != nil {
		cobra.CompErrorln(errors.WithMessage(err, "failed to list changes").Error())
		return nil, cobra.ShellCompDirectiveError
	}
	if len(items) == 0 && withBranches {
		defaultBranchName, err := c.Git.GetDefaultBranchName(ctx, workTree, git.DefaultRemote)
		if errors.Is(err, git.ErrBranchNotFound) {
			// do nothing
		} else if err != nil {
			cobra.CompErrorln(errors.WithMessage(err, "failed to get default branch name").Error())
			return nil, cobra.ShellCompDirectiveError
		} else {
			branch, err := c.Git.GetBranch(ctx, workTree, defaultBranchName)
			if err != nil {
				cobra.CompErrorln(errors.WithMessage(err, "failed to get default branch").Error())
				return nil, cobra.ShellCompDirectiveError
			}
			items = []changemodel.ChangeListItem{
				{
					Branch: branch,
				},
			}
		}
	}

	result := make([]string, len(items))
	for i, item := range items {
		if item.Change != nil {
			result[i] = item.Change.ID.String() + "\t" + item.Change.Title
		} else if item.Branch != nil {
			result[i] = item.Branch.Name + "\t" + item.Branch.Commit.Subject()
		}
	}

	return lo.Reverse(result), cobra.ShellCompDirectiveKeepOrder | cobra.ShellCompDirectiveNoFileComp
}
