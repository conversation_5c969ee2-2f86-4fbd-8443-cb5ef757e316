package cmd

import (
	"fmt"
	"os"
	"strings"

	"github.com/spf13/cobra"
	"go.uber.org/fx"

	"code.byted.org/vecode/staircase/util/build"
	"code.byted.org/vecode/staircase/util/cmd"
	"code.byted.org/vecode/staircase/util/gitdb"
)

type NewRootCommandOpt struct {
	fx.In
	Registrants []cmd.Registrant `group:"command_registrants"`
}

func NewRootCommand(opt NewRootCommandOpt) (*cobra.Command, error) {
	root := &cobra.Command{
		Use:          "st",
		Short:        "A cli tool for managing stacked diffs",
		SilenceUsage: true,
		Version:      fmt.Sprintf("%s\nbuild date: %s\nbuild commit: %s\ncommit date: %s", build.Version, build.Date, build.CommitID, build.CommittedAt),
		PersistentPreRun: func(cmd *cobra.Command, _ []string) {
			cmd.SetContext(gitdb.WithAnnotation(cmd.Context(), strings.Join(os.Args[1:], " ")))
		},
		CompletionOptions: cobra.CompletionOptions{
			HiddenDefaultCmd: true,
		},
	}
	root.SetHelpTemplate(`{{if .Short}}{{.Short | trimTrailingWhitespaces}}{{end}}{{if .Long}}

Synopsis:
{{.Long | trimTrailingWhitespaces}}{{end}}{{if or .Runnable .HasSubCommands}}

{{.UsageString}}{{end}}`)
	for _, registrant := range opt.Registrants {
		err := registrant.Register(root)
		if err != nil {
			return nil, err
		}
	}
	return root, nil
}
